const { defineConfig } = require('@vue/cli-service');
const webpack = require('webpack');
const path = require('path');
const fs = require('fs');
const ip = require('ip');
const getAppConfig = require('./app-env/config.js');
const { dateFormat } = require('./build/utils');
const { configEntry } = require('@paas/paas-library/compiler/index.js');

const antdStyle = require('@paas/paas-library/src/theme/antd-sty.json');
const { PaasCheckPlugin } = require('@paas/paas-webpack-plugin');

const FRAMEWORK_CONFIG_URL = 'public/framework.config.json';

const resolve = dir => path.join(__dirname, dir); // 路径

const getConfigJson = function () {
	return require(resolve(FRAMEWORK_CONFIG_URL));
};

const isProd = process.argv.includes('--isProd');
const isDev = process.argv.includes('--isDev');
const isLocal = process.argv.includes('--isLocal');
const isTest = process.argv.includes('--isTest');

const setEnvParams = function () {
	const realUrl = resolve(FRAMEWORK_CONFIG_URL);
	const framework = require(realUrl);

	// 添加版本号，控制缓存
	if (!isLocal) {
		const hasTemptime = /-[0-9]{8}/.test(framework.version);
		const tempstimeIndex = framework.version.lastIndexOf('-');
		if (hasTemptime) {
			framework.version = framework.version.slice(0, tempstimeIndex) + '-' + dateFormat('MMddhhmm');
		} else {
			framework.version = framework.version + '-' + dateFormat('MMddhhmm');
		}
	}

	const frameworkStr =
		JSON.stringify(framework, null, '\t').replace(new RegExp(/\[[^,]*]/g), str => str.replace(/[\s\t]/g, '')) +
		'\n';

	fs.writeFile(realUrl, frameworkStr, 'utf-8', () => {
		console.log('config配置环境相关配置写入成功');
	});
};

const appConfig = getAppConfig(isProd, isDev, isLocal, isTest);

console.log('isProd, isDev, isLocal, isTest', isProd, isDev, isLocal, isTest);

const port = 8022;

module.exports = defineConfig({
	lintOnSave: true,
	publicPath: './',
	productionSourceMap: false,
	outputDir: 'dist',
	devServer: {
		port,
		headers: {
			// 'Access-Control-Allow-Origin': '*'
		},
		client: {
			webSocketURL: `wss://${ip.address().replace(/\./g, '-')}-${port}.local.mucang.cn/ws`
		}
	},
	css: {
		loaderOptions: {
			less: {
				lessOptions: {
					modifyVars: antdStyle,
					javascriptEnabled: true
				}
			}
		}
	},
	transpileDependencies: ['@paas/paas-library'],

	chainWebpack: config => {
		// 移除 prefetch 插件
		config.plugins.delete('prefetch');
		// 不同环境对应不同的map
		if (isLocal || isDev) {
			config.devtool('source-map');
		} else if (isTest) {
			config.devtool('cheap-source-map');
		}
		configEntry(config);

		config.resolve.alias.set('@', resolve('src'));
		config.name = appConfig.enName;
		config.optimization.chunkIds = 'named';
		// 依赖包拆分和代码拆分
		config.optimization.splitChunks({
			chunks: 'all',
			// 按需加载的代码块（vendor-chunk）并行请求的数量小于或等于10个
			maxAsyncRequests: 10,
			maxInitialRequests: 50, // 最大初始化请求
			minSize: 100000, // 依赖包超过100kb将被单独打包
			cacheGroups: {
				libs: {
					name(module) {
						//  拆分每个 npm 包
						let packageName = Date.now().toString() + Math.floor(Math.random() * 10);
						let npmName = module.context.match(/[\\/]node_modules[\\/](.*?)([\\/]|$)/);
						if (npmName && npmName[1]) {
							packageName = npmName[1];
						}
						return `npm.${packageName.replace('@', '')}`;
					},
					test: /[\\/]node_modules[\\/]/,
					priority: 10,
					chunks: 'initial' // only package third parties that are initially dependent
				},
				appModule: {
					name(module) {
						let packageName = module.context.match(/[\\/]application[\\/](.*?)([\\/]|$)/);
						if (packageName) {
							packageName = packageName[1];
						} else {
							packageName = Date.now().toString();
						}
						return `application.${packageName}`;
					},
					test: /[\\/]src[\\/]_?application(.*)/,
					priority: 8,
					enforce: true
				}
			}
		});
	},
	configureWebpack: config => {
		config.name = '驾考宝典企业版·木仓科技荣誉出品';
		config.plugins.push(
			new PaasCheckPlugin({
				checkCodePath: resolve('src/background'),
				words: ['@paas/paas-library'],
				checkTypePath: resolve('src'),
				callback: () => {
					setEnvParams();
				}
			}), // 定义全局变量
			new webpack.DefinePlugin({
				ConfigJson: JSON.stringify(getConfigJson()),
				APP: JSON.stringify(appConfig)
			})
		);

		config.cache = {
			// 将缓存类型设置为文件系统, 默认是memory
			type: 'filesystem',
			buildDependencies: {
				// 更改配置文件时，重新缓存
				config: [__filename]
			},
			name: 'AppBuildCache'
		};

		let snapshot = config.snapshot || {};

		snapshot.managedPaths = [/^(.+?[\\/]node_modules)[\\/]((?!@paas)).*[\\/]*/];
		config.snapshot = snapshot;

		// 添加对 @tanstack/vue-query 的特殊处理
		// config.module.rules.push({
		// 	test: /\.js$/,
		// 	include: [
		// 		path.resolve(__dirname, 'node_modules/@tanstack')
		// 	],
		// 	use: {
		// 		loader: 'babel-loader',
		// 		options: {
		// 			presets: [
		// 				['@babel/preset-env', {
		// 					targets: {
		// 						chrome: '68'
		// 					}
		// 				}]
		// 			]
		// 		}
		// 	}
		// });
	}
});
