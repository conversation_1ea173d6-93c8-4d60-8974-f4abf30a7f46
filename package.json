{"name": "buick", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve --mode development --isLocal", "build:test": "vue-cli-service build --mode production --isTest", "build:dev": "vue-cli-service build --mode production --isDev", "build": "vue-cli-service build --mode production --isProd", "lint": "vue-cli-service lint", "format": "prettier --write \"src/**/*.ts\" \"src/**/*.ts\" \"src/**/*.vue\" \"src/**/*.less\""}, "dependencies": {"@ant-design/icons-vue": "6.1.0", "@paas/paas-base-lib": "2.0.1", "@paas/paas-library": "1.1.171", "@paas/paas-qrcodejs": "1.0.1", "@tanstack/vue-query": "^5.76.0", "core-js": "3.23.4", "dayjs": "^1.11.9", "mitt": "^3.0.0", "pdfjs-dist": "5.3.31", "pinia": "2.1.7", "qrcode": "^1.5.3", "vue": "3.4.4", "vue-router": "4.2.5"}, "devDependencies": {"@babel/plugin-proposal-optional-chaining": "^7.18.9", "@paas/eslint-config-paas": "^2.0.6", "@paas/paas-webpack-plugin": "^1.0.0", "@types/node": "18.0.0", "@typescript-eslint/eslint-plugin": "^5.30.6", "@typescript-eslint/parser": "^5.30.6", "@vue/cli-plugin-babel": "^5.0.8", "@vue/cli-plugin-eslint": "^5.0.8", "@vue/cli-plugin-router": "^5.0.8", "@vue/cli-plugin-typescript": "^5.0.8", "@vue/cli-service": "^5.0.8", "@vue/eslint-config-typescript": "^9.1.0", "eslint": "^7.32.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-vue": "^8.0.3", "husky": "^4.2.5", "ip": "^1.1.8", "less": "^4.0.0", "less-loader": "^6.0.0", "prettier": "^2.4.1", "typescript": "~4.7.4"}, "eslintConfig": {"root": true, "env": {"browser": true, "node": true, "es6": true}, "globals": {"APP": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended", "@vue/typescript/recommended", "@paas/eslint-config-paas"], "parserOptions": {"sourceType": "module"}, "plugins": ["prettier"], "rules": {"no-empty-function": "off", "@typescript-eslint/no-empty-function": "off", "@typescript-eslint/no-inferrable-types": "off", "no-unused-vars": "off"}}, "husky": {"hooks": {"pre-commit": "npm run lint"}}, "browserslist": ["> 1%", "Chrome 68", "not dead", "not ie 11"]}