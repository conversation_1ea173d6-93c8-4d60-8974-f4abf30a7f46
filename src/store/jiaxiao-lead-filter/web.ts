import { AgentStore, MethodTypeModel } from '@paas/paas-library';

/**
 * 获取电话线索服务的状态
 */
export class JiaxiaoPhoneLeadStore<T> extends AgentStore<T> {
	url = 'jiaxiao-lead-filter://api/web/jiaxiao-phone-lead/view.htm';
	method: MethodTypeModel = 'GET';
}

/**
 * 关闭电话线索服务
 */
export class ClosePhoneClueStore<T> extends AgentStore<T> {
	url = 'jiaxiao-lead-filter://api/web/jiaxiao-phone-lead/close.htm';
	method: MethodTypeModel = 'POST';
}

/**
 * 开启电话线索服务
 */
export class OpenPhoneClueStore<T> extends AgentStore<T> {
	url = 'jiaxiao-lead-filter://api/web/jiaxiao-phone-lead/open.htm';
	method: MethodTypeModel = 'POST';
}

// 删除招生电话
export class DelPhoneStore<T> extends AgentStore<T> {
	url = 'jiaxiao-lead-filter://api/web/jiaxiao-phone-lead/delete-phone.htm';
	method: MethodTypeModel = 'POST';
}

// 招生电话验证码
export class SendVerifyCodeStore<T> extends AgentStore<T> {
	url = 'jiaxiao-lead-filter://api/web/jiaxiao-phone-lead/send-verify-code.htm';
	method: MethodTypeModel = 'GET';
}

// 修改招生电话-手机号
export class EnrollPhoneStore<T> extends AgentStore<T> {
	url = 'jiaxiao-lead-filter://api/web/jiaxiao-phone-lead/submit-with-verify-code.htm';
	method: MethodTypeModel = 'POST';
}

// 修改招生电话-固话
export class EnrollTelStore<T> extends AgentStore<T> {
	url = 'jiaxiao-lead-filter://api/web/jiaxiao-phone-lead/submit-without-verify-code.htm';
	method: MethodTypeModel = 'POST';
}

// 修改招生电话-手机号-副号
export class EnrollSparePhoneStore<T> extends AgentStore<T> {
	url = 'jiaxiao-lead-filter://api/web/jiaxiao-phone-lead/submit-spare-phone-with-verify-code.htm';
	method: MethodTypeModel = 'POST';
}

// 修改招生电话-固话-副号
export class EnrollSpareTelStore<T> extends AgentStore<T> {
	url = 'jiaxiao-lead-filter://api/web/jiaxiao-phone-lead/submit-spare-phone.htm';
	method: MethodTypeModel = 'POST';
}
