import { AgentStore, MethodTypeModel } from '@paas/paas-library';

/**
 * 线索历史列表
 */
export class ClueHistoryStatStore<T> extends AgentStore<T> {
	url = 'volvo://api/h5/auth/lead/lead/stat-history.htm';
	method: MethodTypeModel = 'GET';
}

/**
 * 获取批量购买线索弹窗通知
 */
export class LeadBatchOrderNotifyStore<T> extends AgentStore<T> {
	url = 'volvo://api/h5/auth/lead/lead/get-lead-batch-order-notify.htm';
	method: MethodTypeModel = 'GET';
}

/**
 * 获取推送通知线索价格
 */
export class LeadBatchNotifyPriceStore<T> extends AgentStore<T> {
	url = 'volvo://api/h5/auth/lead/lead/get-batch-notify-price.htm';
	method: MethodTypeModel = 'POST';
}

/**
 * 推送折扣订单购买
 */
export class LeadBatchPayByNotifyStore<T> extends AgentStore<T> {
	url = 'volvo://api/h5/auth/lead/lead/batch-pay-by-notify.htm';
	method: MethodTypeModel = 'POST';
}

/**
 * 是否有待支付的批量购买线索订单
 */
export class HasOrderListStore<T> extends AgentStore<T> {
	url = 'volvo://api/h5/auth/lead/lead/has-wait-pay-batch-order.htm';
	method: MethodTypeModel = 'GET';
}

/**
 * 批量购买线索订单列表
 */
export class BatchOrderListStore<T> extends AgentStore<T> {
	url = 'volvo://api/h5/auth/lead/lead/batch-order-list.htm';
	method: MethodTypeModel = 'GET';
}

/**
 * 通过批量订单来购买线索
 */
export class BatchOrderPayByOrderStore<T> extends AgentStore<T> {
	url = 'volvo://api/h5/auth/lead/lead/batch-pay-by-order.htm';
	method: MethodTypeModel = 'POST';
}

/**
 * 复制批量线索购买订单
 */
export class BatchOrderCopyStore<T> extends AgentStore<T> {
	url = 'volvo://api/h5/auth/lead/lead/copy-batch-order.htm';
	method: MethodTypeModel = 'POST';
}

/**
 * 取消批量线索购买订单
 */
export class BatchOrderCancelStore<T> extends AgentStore<T> {
	url = 'volvo://api/h5/auth/lead/lead/cancel-batch-order.htm';
	method: MethodTypeModel = 'POST';
}

/**
 * 是否能查看电话拨打记录
 */
export class ShowPhoneRecordStore<T> extends AgentStore<T> {
	url = 'volvo://api/h5/auth/lead/lead/show-lead-phone-record.htm';
	method: MethodTypeModel = 'GET';
}
