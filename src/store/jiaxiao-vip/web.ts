import { AgentStore, MethodTypeModel } from '@paas/paas-library';

/**
 * 短信模版
 */
export class MessageTemplateStore<T> extends AgentStore<T> {
	url = 'jiaxiao-vip://api/web/admin/sms-package/sms-template.htm';
	method: MethodTypeModel = 'GET';
}

/**
 * 短信包价格
 */
export class SMSPriceStore<T> extends AgentStore<T> {
	url = 'jiaxiao-vip://api/web/admin/sms-package/sms-price.htm';
	method: MethodTypeModel = 'GET';
}

/**
 * 短信权益包明细
 */
export class DailyRecordStore<T> extends AgentStore<T> {
	url = 'jiaxiao-vip://api/web/admin/sms-package/daily-record.htm';
	method: MethodTypeModel = 'GET';
}

/**
 * 是否开通留资，电话线索
 */
export class JiaxiaoConfigStore<T> extends AgentStore<T> {
	url = 'jiaxiao-vip://api/web/admin/jiaxiao-config/view.htm';
	method: MethodTypeModel = 'GET';
}

/**
 * 充值
 */
export class RechargeStore<T> extends AgentStore<T> {
	url = 'jiaxiao-vip://api/web/admin/account/recharge.htm';
	method: MethodTypeModel = 'POST';
}

/**
 * 查询订单状态
 */
export class StatusStore<T> extends AgentStore<T> {
	url = 'jiaxiao-vip://api/web/admin/account/status.htm';
	method: MethodTypeModel = 'GET';
}

/**
 * 获取驾校账户信息
 */
export class AccountInfoStore<T> extends AgentStore<T> {
	url = 'jiaxiao-vip://api/web/admin/account/view.htm';
	method: MethodTypeModel = 'GET';
}

/**
 * 获取驾校元宝账户信息
 */
export class AccountAndCoinInfoStore<T> extends AgentStore<T> {
	url = 'jiaxiao-vip://api/web/admin/coin/amount.htm';
	method: MethodTypeModel = 'GET';
}

/**
 * 获取元宝购买配置
 */
export class CoinGoodsInfo<T> extends AgentStore<T> {
	url = 'jiaxiao-vip://api/web/admin/coin/get-coin-goods-info.htm';
	method: MethodTypeModel = 'GET';
}

/**
 * 查询是否能购买，驾校是否已被加入黑名单
 */
export class CoinIsBuyStore<T> extends AgentStore<T> {
	url = 'jiaxiao-vip://api/web/admin/coin/is-buy.htm';
	method: MethodTypeModel = 'GET';
}

/**
 * 元宝交易明细
 */
export class CoinDetailListStore<T> extends AgentStore<T> {
	url = 'jiaxiao-vip://api/web/admin/coin/detail-list.htm';
	method: MethodTypeModel = 'GET';
}

/**
 * 商品中心交易明细
 */
export class AccountDetailListStore<T> extends AgentStore<T> {
	url = 'jiaxiao-vip://api/web/admin/account/account-detail-list.htm';
	method: MethodTypeModel = 'GET';
}

/**
 * 对公转账记录
 */
export class BankTransferListStore<T> extends AgentStore<T> {
	url = 'jiaxiao-vip://api/web/admin/account/bank-transfer-list.htm';
	method: MethodTypeModel = 'GET';
}

/**
 * 获取分销Url
 */
export class SaleExchangeShareUrlStore<T> extends AgentStore<T> {
	url = 'jiaxiao-vip://api/web/admin/sale-exchange/get-share-url.htm';
	method: MethodTypeModel = 'GET';
}

/**
 * 分销用户明细
 */
export class SaleExchangeListStore<T> extends AgentStore<T> {
	url = 'jiaxiao-vip://api/web/admin/sale-exchange/list.htm';
	method: MethodTypeModel = 'GET';
}

/**
 * 电话线索包明细
 */
export class LeadPackageDetailStore<T> extends AgentStore<T> {
	url = 'jiaxiao-vip://api/web/admin/lead-package/get-detail.htm';
	method: MethodTypeModel = 'GET';
}

/**
 * 电话线索包配置
 */
export class LeadPackageStore<T> extends AgentStore<T> {
	url = 'jiaxiao-vip://api/web/admin/lead-package/get-goods-list.htm';
	method: MethodTypeModel = 'GET';
}

/**
 * 电话线索包展示
 */
export class LeadPackageViewStore<T> extends AgentStore<T> {
	url = 'jiaxiao-vip://api/web/admin/lead-package/view.htm';
	method: MethodTypeModel = 'GET';
}

/**
 * 短信包账户信息
 */
export class SMSPackageAccountStore<T> extends AgentStore<T> {
	url = 'jiaxiao-vip://api/web/admin/sms-package/account.htm';
	method: MethodTypeModel = 'GET';
}

/**
 * 用户会员信息
 */
export class UserVipStore<T> extends AgentStore<T> {
	url = 'jiaxiao-vip://api/web/admin/vip/my-vip.htm';
	method: MethodTypeModel = 'GET';
}

/**
 * 是否开通分销活动
 */
export class SaleExchangeIsOpenStore<T> extends AgentStore<T> {
	url = 'jiaxiao-vip://api/web/admin/sale-exchange/is-open.htm';
	method: MethodTypeModel = 'GET';
}

/**
 * 会员权益明细
 */
export class VipRightsDetailStore<T> extends AgentStore<T> {
	url = 'jiaxiao-vip://api/web/admin/vip/vip-detail-list.htm';
	method: MethodTypeModel = 'GET';
}

// 支付账户授权列表
export class PayAccountAuthListStore<T> extends AgentStore<T> {
	url = 'jiaxiao-vip://api/web/admin/assigned-pay-account/list.htm';
	method: MethodTypeModel = 'POST';
}

// 批量授权
export class BatchAuthStore<T> extends AgentStore<T> {
	url = 'jiaxiao-vip://api/web/admin/assigned-pay-account/batch-authorization.htm';
	method: MethodTypeModel = 'POST';
}

// 支付账户授权 payNumber
export class PayAccountAuthPayNumberStore<T> extends AgentStore<T> {
	url = 'jiaxiao-vip://api/web/admin/assigned-pay-account/authorization-by-pay-number.htm';
	method: MethodTypeModel = 'POST';
}

// 查询账户授权信息
export class AuthJugeInfoStore<T> extends AgentStore<T> {
	url = 'jiaxiao-vip://api/web/admin/assigned-pay-account/get-authorization-juge-info.htm';
	method: MethodTypeModel = 'GET';
}

// 查询授权结果
export class AuthResultStore<T> extends AgentStore<T> {
	url = 'jiaxiao-vip://api/web/admin/assigned-pay-account/query-result.htm';
	method: MethodTypeModel = 'GET';
}

// 支付账户授权弹窗是否展示
export class PayAccountAuthDialogStore<T> extends AgentStore<T> {
	url = 'jiaxiao-vip://api/web/admin/assigned-pay-account/alert-is-open.htm';
	method: MethodTypeModel = 'GET';
}

// 展示位
export class DisplayStore<T> extends AgentStore<T> {
	url = 'jiaxiao-vip://api/h5/auth/vip/display-area/list.htm';
	method: MethodTypeModel = 'GET';
}

// 获取畅享电话卡展示信息
export class ShowInfoStore<T> extends AgentStore<T> {
	url = 'jiaxiao-vip://api/web/admin/phone-equity-card/get-show-info.htm';
	method: MethodTypeModel = 'GET';
}

// 获取畅享电话卡商品明细
export class OrderDetailStore<T> extends AgentStore<T> {
	url = 'jiaxiao-vip://api/web/admin/phone-equity-card/get-order-detail.htm';
	method: MethodTypeModel = 'GET';
}

// 获取畅享电话卡商品信息
export class GoodsInfoStore<T> extends AgentStore<T> {
	url = 'jiaxiao-vip://api/web/admin/phone-equity-card/get-goods-info.htm';
	method: MethodTypeModel = 'GET';
}

// 获取畅享电话卡商品信息
export class PaySuccessStore<T> extends AgentStore<T> {
	url = 'jiaxiao-vip://api/web/admin/phone-equity-card/pay-success.htm';
	method: MethodTypeModel = 'GET';
}
// 开通的培训类型
export class OpenedTrainTypesStore<T> extends AgentStore<T> {
	url = 'jiaxiao-vip://api/web/admin/dictionary/opened-train-types.htm';
	method: MethodTypeModel = 'GET';
}
// 所以驾照类型
export class AllDriveLicensesStore<T> extends AgentStore<T> {
	url = 'jiaxiao-vip://api/web/admin/dictionary/drive-licenses.htm';
	method: MethodTypeModel = 'GET';
}

// 是否隐藏定向非定向
export class IsHiddenDirectedStore<T> extends AgentStore<T> {
	url = 'jiaxiao-vip://api/web/admin/jiaxiao-config/is-hidden1v1-lead-jx.htm';
	method: MethodTypeModel = 'GET';
}
