import { AgentStore, MethodTypeModel } from '@paas/paas-library';

/**
 * 查询代付款协议信息
 */
export class AgreementViewStore<T> extends AgentStore<T> {
	url = 'jiaxiao-vip://api/web/admin/agreement/view.htm';
	method: MethodTypeModel = 'GET';
}

/**
 * 下载协议模板
 */
export class AgreementDownloadTemplateStore<T> extends AgentStore<T> {
	url = 'jiaxiao-vip://api/web/admin/agreement/download-template.htm';
	method: MethodTypeModel = 'GET';
}

/**
 * 提交协议
 */
export class AgreementSubmitStore<T> extends AgentStore<T> {
	url = 'jiaxiao-vip://api/web/admin/agreement/submit.htm';
	method: MethodTypeModel = 'POST';
}
