import { OpenedTrainTypesResponse } from '@/utils/types/clue';

// 驾校已开通的培训类型
export interface OpenedTrainTypesModel {
	list: OpenedTrainTypesResponse[];
	hasCar: boolean;
	hasUav: boolean;
	isCarAndUav: boolean;
	isCar: boolean;
	isUav: boolean;
}

// 代付款协议信息
export interface AgreementViewResponse {
	/** 商家Id */
	targetId: number;
	/** 协议签署状态 true已签署,false未签署 */
	signed: boolean;
	/** 协议文件url */
	fileUrl: string;
	/** 协议文件encodeData */
	encodeData: string;
	/** 协议过期时间 */
	expirationTime: number;
}
