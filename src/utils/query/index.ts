import { AGREEMENT_VIEW, CERTIFICATION_INFO, CONTRACT_RESULT, OPENED_TRAIN_TYPES } from '@/utils/query/key';
import {
	GetAgreementViewStore,
	GetCertificationStatusStore,
	GetContractResultStore,
	GetOpenedTrainTypesStore
} from '@/utils/query/store';
import { useQuery } from '@tanstack/vue-query';
import { TrainTypeEnum, TrainTypeStringEnum } from '@/utils/constants/clue';
import { CertificationResponse } from '@/utils/types/certification';
import { OpenedTrainTypesResponse } from '@/utils/types/clue';
import { AgreementViewResponse, OpenedTrainTypesModel } from '@/utils/query/types';

// 合同签署结果
export const useContractResultStore = () =>
	useQuery({
		queryKey: [CONTRACT_RESULT],
		queryFn: () =>
			GetContractResultStore.request({
				trainType: TrainTypeEnum.UAV
			}).getData(),
		refetchOnWindowFocus: false,
		staleTime: 30 * 1000,
		retry: false
	});

// 驾校认证信息
export const useCerInfoStore = () =>
	useQuery<CertificationResponse>({
		queryKey: [CERTIFICATION_INFO],
		queryFn: () => GetCertificationStatusStore.request().getData(),
		refetchOnWindowFocus: false,
		staleTime: 30 * 1000,
		retry: false
	});

// 驾校开通的培训类型
export const useOpenedTrainTypesStore = () =>
	useQuery<{ itemList: OpenedTrainTypesResponse[] }, Error, OpenedTrainTypesModel>({
		queryKey: [OPENED_TRAIN_TYPES],
		queryFn: () => GetOpenedTrainTypesStore.request().getData(),
		refetchOnWindowFocus: false,
		retry: false,
		initialData: {
			itemList: []
		},
		select: data => {
			const list = data?.itemList || [];
			const hasCarType = list.some(item => item.code === TrainTypeStringEnum.CAR);
			const hasUavType = list.some(item => item.code === TrainTypeStringEnum.UAV);

			return {
				list,
				// 是否有汽车培训类型
				hasCar: hasCarType,
				// 是否有无人机培训类型
				hasUav: hasUavType,
				// 是否有汽车和无人机培训类型
				isCarAndUav: hasCarType && hasUavType,
				// 是否只有汽车培训类型
				isCar: hasCarType && !hasUavType,
				// 是否只有无人机培训类型
				isUav: !hasCarType && hasUavType
			};
		}
	});

// 代付款协议信息
export const useAgreementViewStore = () =>
	useQuery<AgreementViewResponse>({
		queryKey: [AGREEMENT_VIEW],
		queryFn: () => GetAgreementViewStore.request().getData(),
		refetchOnWindowFocus: false,
		staleTime: 30 * 1000,
		retry: false
	});
