import { ContractResultStore } from '@/store/benz/contract-sign';
import { CertificationResponse, ContractResultResponse } from '@/utils/types/certification';
import { OpenedTrainTypesStore } from '@/store/jiaxiao-vip/web';
import { OpenedTrainTypesResponse } from '@/utils/types/clue';
import { CertificationStatusStore } from '@/store/benz/jiaxiao-certificate';
import { AgreementViewStore } from '@/store/jiaxiao-vip/agreement';

import { AgreementViewResponse } from '@/utils/query/types';

// 查看认证状态
export const GetCertificationStatusStore = new CertificationStatusStore<CertificationResponse>({});

// 获取合同签署结果
export const GetContractResultStore = new ContractResultStore<ContractResultResponse>({
	errToast: false
});

// 获取驾校开通的培训类型
export const GetOpenedTrainTypesStore = new OpenedTrainTypesStore<{ itemList: OpenedTrainTypesResponse[] }>({});

// 代付款协议信息
export const GetAgreementViewStore = new AgreementViewStore<AgreementViewResponse>({});
