import { OwnershipStatusEnum, StatusEnum, UavOwnershipStatusEnum, UserTypeEnum } from '@/utils/constants/certification';

// 合同签署结果
export interface ContractResultResponse {
	signStatus: UavOwnershipStatusEnum;
	signUrl: string;
	specialApproval: boolean;
}

// 驾校认证信息
export interface CertificationResponse {
	/** 驾校ID */
	jiaxiaoId: number;
	/** 电话 */
	userPhone: string;
	/** 电话掩码 */
	userPhoneMask: string;
	/** 驾校用户类型，0驾校，1分校 2代理 */
	userType: UserTypeEnum;
	/** 驾校名称 */
	jiaxiaoName: string;
	/** 城市编码 */
	cityCode: string;
	/** 城市名称 */
	cityName: string;
	/** 驾校全称 */
	jiaxiaoFullName: string;
	/** 管理员姓名 */
	userName: string;
	/** 总校全称 */
	masterJiaxiaoName: string;
	/** 总校基础库企业id */
	masterBaseEnterpriseId: number;
	/** 认证状态：-1未提交，0待审核，1审核通过，2审核失败，3更新认证待审核，4更新认证审核失败 */
	status: StatusEnum;
	// enterpriseOperatorStatus?: StatusEnum;
	/** 财务审核状态，-1待提交，0待运营对公打款，1验证成功，2验证失败,3打款结果等待中,4待提交打款验证,10待提交银行账号,5企业账户信息认证失败 */
	// financeApproveStatus: FinanceApproveStatusEnum;
	/** 财务打款免审核截止时间 */
	financeAuditExemptionExpireTime: number;
	/** 不通过原因 */
	remark: string | string[];
	// 招生授权状态
	ownershipStatus: OwnershipStatusEnum;
}
