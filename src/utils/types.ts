import { UserTypeEnum } from '@/utils/constants/certification';

// 驾校配置
export interface JiaxiaoConfigResponse {
	allTargetInquiry: boolean;
	allTargetInquiryCity: string;
	allTargetInquiryLicense?: string;
	allow1V1Inquiry: boolean;
	allow1VNInquiry: boolean;
	associatedMerchantInfo?: string;
	basicClueService: boolean;
	// 无人机线索服务状态
	uaClueService: boolean;
	bindCrm: boolean;
	canBuy: boolean;
	canBuyTime: number;
	cityCode: string;
	clueAreaCityWide: number;
	clueCategory: number;
	clueSourceReceiveCategory: number;
	coinRecharge: boolean;
	coinRechargeTime: number;
	cooperationClueNum: number;
	cooperationType: number;
	createTime: number;
	createUserId: string;
	createUserName: string;
	crmLeadBeginTime: number;
	crmLeadEndTime?: number;
	dailyClueLimit: number;
	dailyClueLimitList: number[];
	dailyClueLimitUpdateChanceCount: number;
	disabledDriveLicense: string;
	driveLicenseUpdateLimit: number;
	enableEndTime: number;
	enableStartTime: number;
	id: number;
	jiaxiaoId: number;
	jiaxiaoName: string;
	leadAppeal: boolean;
	lexusLeadBeginTime?: number;
	lexusLeadEndTime?: number;
	limitCityList: string;
	phoneClueService: boolean;
	priority: number;
	receiveAllTargetInquiryUpdateChanceCount: number;
	recommendJiaxiao: boolean;
	recruitSiteLimit: number;
	supplementCityList: string;
	updateTime: number;
	updateUserId: string;
	updateUserName: string;
	userType: UserTypeEnum;
}
