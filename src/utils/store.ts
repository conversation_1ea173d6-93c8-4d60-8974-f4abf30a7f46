import { PaasListResponse } from '@paas/paas-library';
import { JiaxiaoConfigStore, AllDriveLicensesStore, IsHiddenDirectedStore } from '@/store/jiaxiao-vip/web';
import { JiaxiaoConfigResponse } from '@/utils/types';
import { ShowPhoneRecordStore } from '@/store/volvo/h5';
import { OpenedTrainTypesResponse } from '@/utils/types/clue';

// 是否开通留资，电话线索
export const GetJiaxiaoConfigStore = new JiaxiaoConfigStore<JiaxiaoConfigResponse>({});

// 是否能查看电话拨打记录
export const GetShowPhoneRecordStore = new ShowPhoneRecordStore<{ value: boolean }>({});

// 所有驾照类型
export const GetAllDriveLicensesStore = new AllDriveLicensesStore<PaasListResponse<OpenedTrainTypesResponse>>({});

// 获取驾校是否隐藏定向非定向
export const GetJiaxiaoHideDirectedStore = new IsHiddenDirectedStore<{ value: boolean }>({});
