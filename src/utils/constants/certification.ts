export enum UserTypeEnum {
	// 主校
	MAIN,
	// 分校
	BRANCH,
	// 代理
	AGENT
}

export const enum StatusEnum {
	// 未提交
	NOT_SUBMIT = -1,
	// 待审核
	REVIEW,
	// 审核通过
	SUCCESS,
	// 审核失败
	FAIL,
	// 更新认证待审核
	UPDATE_REVIEW,
	// 更新认证审核失败
	UPDATE_FAIL
}

// 招生授权状态
export const enum OwnershipStatusEnum {
	// 未提交认证
	UN_SUBMIT = 'UN_SUBMIT',
	// 认证中
	WAITING = 'WAITING',
	// 未认证通过
	IN_AUDITING = 'IN_AUDITING',
	// 认证通过
	APPROVED = 'APPROVED',
	// 认证失败
	FAIL = 'FAIL'
}

// 无人机招生授权状态
export enum UavOwnershipStatusEnum {
	// 未提交认证
	UN_SUBMIT = 10,
	// 认证中
	WAITING = 20,
	// 认证通过
	APPROVED = 30
}
