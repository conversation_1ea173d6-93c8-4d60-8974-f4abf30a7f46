import { AuthResultEnum, GoodsTypeEnum } from '@/pinia/open-pay-account-auth/constants';
import { StatusModel } from '@/pinia/recharge/types';
import { ConnectEnum } from '@/components/pay-account-auth-dialog/constants';
import { ActionEnum } from '@/utils/constants/common';
export interface StateModel
	extends CustomParamsModel,
		Pick<IsNeedModel, 'goodsType'>,
		Partial<AccountIdOpenParamsModel>,
		Pick<AuthJugeInfoResponse, 'amount' | 'failRemark' | 'name' | 'phoneMask' | 'relationship'> {
	visible: boolean;
	// 授权状态
	status: AuthResultEnum;
	// 是否在轮询
	statusLoading: boolean;
}

export interface CustomParamsModel {
	// 展开弹窗前的回调
	beforeCallback?: (params: Pick<AccountAuthCallbackDataModel, 'isNeedAuth' | 'err'>) => unknown;
	// 授权结束后的回调
	callback?: (params: AccountAuthCallbackDataModel) => unknown;
}

// 授权回调
export interface AccountAuthCallbackDataModel {
	// 授权结果状态
	status: AuthResultEnum;
	// 是否需要授权
	isNeedAuth?: boolean;
	action: ActionEnum;
	err?: boolean;
}

// 授权列表直接打开授权弹窗的参数
export interface AccountIdOpenParamsModel
	extends Pick<AuthJugeInfoResponse, 'typeShow' | 'account'>,
		CustomParamsModel {
	payNumber?: string;
}

// 充值完判断是否需要打开授权弹窗的参数
export interface IsNeedModel extends Omit<StatusModel, 'openTimeout'>, CustomParamsModel {
	goodsType?: GoodsTypeEnum;
	hideDialog?: boolean;
}

export interface AuthJugeInfoResponse {
	/**  是否需要授权 */
	needAuthorization: boolean;
	/** 账户类型 */
	type: string;
	/** 账户类型展示 */
	typeShow: string;
	/** 支付账户 */
	account: string;
	amount: number;
	failRemark: string;
	name: string;
	phoneMask: string;
	relationship: ConnectEnum;
}

export interface AuthStatusParamsModel {
	id: number;
}

// 查询授权结果
export interface AuthResultResponse {
	value: AuthResultEnum;
}
