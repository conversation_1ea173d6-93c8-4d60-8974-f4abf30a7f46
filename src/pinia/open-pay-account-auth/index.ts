import { PayTypeEnum } from '@/components/pay-type/constants';
import { AuthResultEnum } from '@/pinia/open-pay-account-auth/constants';
import { GetAuthJugeInfoStore, GetAuthResultStore } from '@/pinia/open-pay-account-auth/store';
import {
	AccountIdOpenParamsModel,
	AuthStatusParamsModel,
	IsNeedModel,
	StateModel
} from '@/pinia/open-pay-account-auth/types';
import { NAME_MAP } from '@/pinia/store-name';
import { MUtils } from '@paas/paas-library';
import { defineStore } from 'pinia';
import { reactive, toRefs } from 'vue';
import { ActionEnum } from '@/utils/constants/common';

// 轮询订单状态的定时器
let statusTimer: NodeJS.Timer = null;
// 5分钟订单超时的定时器
let orderTimeoutTimer: NodeJS.Timer = null;

export const useOpenPayAccountAuthStore = defineStore(NAME_MAP.openPayAccountAuth, () => {
	const state = reactive<StateModel>({
		visible: false,
		amount: null,
		typeShow: '',
		account: '',
		payNumber: '',
		// 展开弹窗前的回调
		beforeCallback: null,
		// 授权结束后的回调
		callback: null,
		// 授权状态
		status: AuthResultEnum.ING,
		// 是否在轮询
		statusLoading: false,
		// 购买的商品类型
		goodsType: null,
		failRemark: '',
		name: '',
		phoneMask: '',
		relationship: null
	});

	const methods = {
		// 清除全部定时器
		clearTimer: (): void => {
			state.statusLoading = false;
			if (statusTimer) {
				clearInterval(statusTimer);
				statusTimer = null;
			}
			if (orderTimeoutTimer) {
				clearTimeout(orderTimeoutTimer);
				orderTimeoutTimer = null;
			}
		},
		// 查询是否需要授权(充值、购买后)
		open(params: IsNeedModel) {
			if ([PayTypeEnum.BALANCE, PayTypeEnum.CMB, PayTypeEnum.OTHER].includes(params.payType)) {
				typeof params.beforeCallback === 'function' &&
					params.beforeCallback({
						isNeedAuth: false
					});
				typeof params.callback === 'function' &&
					params.callback({
						status: AuthResultEnum.SUCCESS,
						isNeedAuth: false,
						action: ActionEnum.AUTO
					});
				return;
			}

			GetAuthJugeInfoStore.request({
				payNumber: params.orderNumber
			})
				.getData()
				.then(data => {
					const { needAuthorization, typeShow, account, amount, failRemark, name, phoneMask, relationship } =
						data;

					typeof params.beforeCallback === 'function' &&
						params.beforeCallback({
							isNeedAuth: needAuthorization
						});
					if (!needAuthorization) {
						typeof params.callback === 'function' &&
							params.callback({
								status: AuthResultEnum.SUCCESS,
								isNeedAuth: false,
								action: ActionEnum.AUTO
							});
						return;
					}

					if (params.goodsType) {
						MUtils.sendTongji([`${params.goodsType}-展示`, '线索管理', '购买线索', '支付账户授权弹窗']);
					}
					state.amount = amount;
					state.typeShow = typeShow;
					state.account = account;
					state.payNumber = params.orderNumber;
					state.callback = params.callback;
					state.goodsType = params.goodsType;
					state.failRemark = failRemark;
					state.name = name;
					state.phoneMask = phoneMask;
					state.relationship = relationship;
					if (!params.hideDialog) {
						state.visible = true;
					}
				})
				.catch(() => {
					typeof params.beforeCallback === 'function' &&
						params.beforeCallback({
							err: true
						});
				});
		},
		// 授权列表直接打开授权弹窗
		accountIdOpen(params: AccountIdOpenParamsModel) {
			state.typeShow = params.typeShow;
			state.account = params.account;
			state.payNumber = params.payNumber;
			state.callback = params.callback;
			state.visible = true;
		},
		// 单次获取授权状态
		checkAuthStatus: (params: AuthStatusParamsModel): Promise<void> => {
			return new Promise(resolve => {
				GetAuthResultStore.request(params)
					.getData()
					.then(data => {
						state.status = data.value;
					})
					.catch(() => {
						state.status = AuthResultEnum.FAIL;
					})
					.finally(() => {
						resolve();
					});
			});
		},
		// 查询授权结果
		fetchStatus: (params: AuthStatusParamsModel): Promise<AuthResultEnum> => {
			methods.clearTimer();
			state.statusLoading = true;

			return new Promise(resolve => {
				statusTimer = setInterval(async () => {
					await methods.checkAuthStatus(params);
					if ([AuthResultEnum.SUCCESS, AuthResultEnum.FAIL].includes(state.status)) {
						methods.clearTimer();
						resolve(state.status);
					}
				}, 1000);

				orderTimeoutTimer = setTimeout(() => {
					methods.clearTimer();
					resolve(AuthResultEnum.ING);
				}, 30 * 1000);
			});
		}
	};

	return {
		...toRefs(state),
		...methods
	};
});
