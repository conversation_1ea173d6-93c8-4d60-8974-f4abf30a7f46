import { CheckCarCerPageIds, CheckTypeEnum, CheckUavCerPageIds } from '@/pinia/certification-info/constant';
import { StateModel } from '@/pinia/certification-info/types';
import { NAME_MAP } from '@/pinia/store-name';
import { useUserProfileStore } from '@/pinia/user-profile';
import { MESSAGE_TYPE, MUtils, PaasPostMessage } from '@paas/paas-library';
import { defineStore } from 'pinia';
import { computed, reactive, toRefs } from 'vue';
import { useCerInfoStore, useContractResultStore, useOpenedTrainTypesStore } from '@/utils/query';
import { OwnershipStatusEnum, StatusEnum, UavOwnershipStatusEnum } from '@/utils/constants/certification';

let dialogCount = 0;

export const useCertificationInfoStore = defineStore(NAME_MAP.certificationInfo, () => {
	const userProfileStore = useUserProfileStore();
	// 机动车认证信息
	const cerInfoStore = useCerInfoStore();
	// 无人机认证信息
	const contractResultStore = useContractResultStore();
	// 驾校开通的培训类型
	const openedTrainTypesStore = useOpenedTrainTypesStore();

	const state = reactive<StateModel>({
		// 当前校验的认证类型
		currentCheckType: null
	});

	const computeds = {
		uavContent: computed(() => {
			if (!contractResultStore.isSuccess.value) {
				return '未获取到驾校无人机招生授权信息，请稍后重试';
			}
			const { signStatus, specialApproval } = contractResultStore.data.value;
			if (specialApproval) {
				return '';
			}
			if (signStatus !== UavOwnershipStatusEnum.APPROVED) {
				return '当前驾校尚未完成无人机招生授权，无法使用此功能，请先完成招生授权。';
			}
			return '';
		}),
		carContent: computed(() => {
			if (!cerInfoStore.isSuccess.value) {
				return {
					content: '未获取到驾校认证信息，请稍后重试',
					financeContent: '未获取到驾校招生授权信息，请稍后重试'
				};
			}

			const { status, ownershipStatus, financeAuditExemptionExpireTime } = cerInfoStore.data.value;
			if (status === StatusEnum.NOT_SUBMIT || status === StatusEnum.REVIEW || status === StatusEnum.FAIL) {
				return {
					content: '当前驾校尚未完成认证，无法使用此功能，请先完成认证。',
					financeContent: ''
				};
			} else if (
				ownershipStatus !== OwnershipStatusEnum.APPROVED &&
				Date.now() > financeAuditExemptionExpireTime
			) {
				return {
					content: '',
					financeContent: '当前驾校尚未完成招生授权，无法使用此功能，请先完成招生授权。'
				};
			} else {
				return {
					content: '',
					financeContent: ''
				};
			}
		})
	};

	const methods = {
		// 校验是否认证
		async checkAuth(type?: CheckTypeEnum): Promise<boolean> {
			// 设置当前页面需要校验的类型
			await methods.setCheckType(type);

			if (
				[CheckTypeEnum.CAR_AND_UAV, CheckTypeEnum.CAR_OR_UAV, CheckTypeEnum.CAR_AUTH_OR_UAV].includes(
					state.currentCheckType
				)
			) {
				await Promise.all([methods.fetchCertificationInfo(), methods.fetchUavCertificationInfo()]);
			} else if (state.currentCheckType === CheckTypeEnum.UAV_OWNER_SHIP) {
				await methods.fetchUavCertificationInfo();
			} else {
				await methods.fetchCertificationInfo();
			}

			let message = '';
			const carMessage = computeds.carContent.value.content || computeds.carContent.value.financeContent;
			const uavMessage = computeds.uavContent.value;

			switch (state.currentCheckType) {
				case CheckTypeEnum.CAR_AND_UAV:
					message = carMessage || uavMessage;
					break;
				case CheckTypeEnum.CAR_OR_UAV:
					if (carMessage && uavMessage) {
						message = carMessage || uavMessage;
					}
					break;
				case CheckTypeEnum.CAR_AUTH_OR_UAV:
					if (computeds.carContent.value.content && uavMessage) {
						message = carMessage || uavMessage;
					}
					break;
				case CheckTypeEnum.CAR:
					message = carMessage;
					break;
				case CheckTypeEnum.CAR_AUTH:
					message = computeds.carContent.value.content;
					break;
				case CheckTypeEnum.CAR_OWNER_SHIP:
					message = computeds.carContent.value.financeContent;
					break;
				case CheckTypeEnum.UAV_OWNER_SHIP:
					message = uavMessage;
					break;
				default:
			}

			if (!message) {
				return true;
			}

			methods.showAlert(message);
			return false;
		},
		// 机动车认证
		fetchCertificationInfo: async (refresh = false): Promise<void> => {
			try {
				if (refresh) {
					await cerInfoStore.refetch();
				} else {
					await cerInfoStore.suspense();
				}
			} catch (err) {
				console.log('cerInfoStore err: ', err);
			}
		},
		// 无人机认证
		fetchUavCertificationInfo: async (refresh = false): Promise<void> => {
			try {
				if (refresh) {
					await contractResultStore.refetch();
				} else {
					await contractResultStore.suspense();
				}
			} catch (err) {
				console.log('contractResultStore err: ', err);
			}
		},
		// 显示提示弹窗
		async showAlert(content: string, callback?: () => unknown) {
			if (dialogCount) {
				return;
			}
			dialogCount++;

			if (!userProfileStore.isSuccess) {
				await userProfileStore.fetchUserProfile();
			}

			const [location, pageInfo, tabList] = await Promise.all([
				PaasPostMessage.post('main://location'),
				PaasPostMessage.post('main://menu.current'),
				PaasPostMessage.post('main://tab.getList')
			]);

			const isSuperAdmin = userProfileStore.isSuperAdmin;
			if (!isSuperAdmin) {
				const _fullId = tabList.find(item => item.id === 'H0028')?._fullId;
				_fullId && PaasPostMessage.post('tab.close', _fullId);
			}

			await MUtils.alert({
				title: '提示',
				content,
				btText: '确定',
				type: MESSAGE_TYPE.warning
			});

			dialogCount--;
			typeof callback === 'function' && callback();

			const { pageId, appName } = await methods.getTargetPageInfo();

			PaasPostMessage.post('base://get.path-jump', {
				id: pageId,
				appName,
				activeMenu: isSuperAdmin ? '' : `buick.${pageInfo.id}`,
				title: isSuperAdmin ? '' : pageInfo.title,
				topUrl: isSuperAdmin ? '' : location.href,
				query: {
					closeTabId: pageInfo.id,
					appName: 'buick'
				}
			});
		},
		// 获取需要跳转的页面
		async getTargetPageInfo() {
			const isSuperAdmin = userProfileStore.isSuperAdmin;

			if (!isSuperAdmin) {
				return {
					pageId: 'H0028',
					appName: ''
				};
			}

			const carMessage = computeds.carContent.value.content || computeds.carContent.value.financeContent;

			// 超级管理员根据校验类型判断跳转地址
			switch (state.currentCheckType) {
				// 无人机相关类型跳转到无人机认证页面
				case CheckTypeEnum.UAV_OWNER_SHIP:
					return {
						pageId: 'W340210',
						appName: 'jaguar'
					};
				// 机动车相关类型跳转到机动车认证页面
				case CheckTypeEnum.CAR:
				case CheckTypeEnum.CAR_AUTH:
				case CheckTypeEnum.CAR_OWNER_SHIP:
					return {
						pageId: 'W50401',
						appName: 'saas'
					};
				// 混合类型需要判断驾校开通的培训类型和认证内容
				case CheckTypeEnum.CAR_AND_UAV:
				case CheckTypeEnum.CAR_OR_UAV:
				case CheckTypeEnum.CAR_AUTH_OR_UAV:
					if (!openedTrainTypesStore.isFetched.value || !openedTrainTypesStore.isSuccess.value) {
						await openedTrainTypesStore.suspense();
					}

					// 如果有机动车认证相关提示，且驾校开通了机动车培训，优先跳转机动车认证页面
					if (carMessage && openedTrainTypesStore.data.value?.hasCar) {
						return {
							pageId: 'W50401',
							appName: 'saas'
						};
					}

					// 如果驾校开通了无人机培训，跳转无人机认证页面
					if (openedTrainTypesStore.data.value?.hasUav) {
						return {
							pageId: 'W340210',
							appName: 'jaguar'
						};
					}

					// 默认跳转机动车认证页面
					return {
						pageId: 'W50401',
						appName: 'saas'
					};
				// 默认跳转机动车认证页面
				default:
					return {
						pageId: 'W50401',
						appName: 'saas'
					};
			}
		},
		// 设置当前页面需要校验的类型
		async setCheckType(type?: CheckTypeEnum): Promise<void> {
			let _type: CheckTypeEnum = null;

			if (type) {
				_type = type;
			} else {
				const [pageInfo] = await Promise.all([PaasPostMessage.post('main://menu.current')]);

				const hasCar = CheckCarCerPageIds.includes(pageInfo.id);
				const hasUav = CheckUavCerPageIds.includes(pageInfo.id);

				if (hasCar && hasUav) {
					_type = CheckTypeEnum.CAR_AND_UAV;
				} else if (hasCar) {
					_type = CheckTypeEnum.CAR;
				} else if (hasUav) {
					_type = CheckTypeEnum.UAV_OWNER_SHIP;
				}
			}

			state.currentCheckType = _type;
		}
	};

	return {
		...toRefs(state),
		...computeds,
		...methods
	};
});
