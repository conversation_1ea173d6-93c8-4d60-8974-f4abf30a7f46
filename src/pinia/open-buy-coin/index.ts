import { useAccountInfoStore } from '@/pinia/account-info';
import { useBlackListStore } from '@/pinia/black-list';
import { useCertificationInfoStore } from '@/pinia/certification-info';
import { OpenModel, StateModel } from '@/pinia/open-buy-coin/types';
import { NAME_MAP } from '@/pinia/store-name';
import { useUserProfileStore } from '@/pinia/user-profile';
import { GetJiaxiaoConfigStore } from '@/utils/store';
import { MESSAGE_TYPE, MUtils, PaasPostMessage } from '@paas/paas-library';
import { defineStore } from 'pinia';
import { reactive, toRefs } from 'vue';
import { useCerInfoStore, useOpenedTrainTypesStore } from '@/utils/query';
import { UserTypeEnum } from '@/utils/constants/certification';

export const useOpenBuyCoinStore = defineStore(NAME_MAP.openBuyCoin, () => {
	const certificationInfoStore = useCertificationInfoStore();
	const userProfileStore = useUserProfileStore();
	const blackListStore = useBlackListStore();
	const accountInfoStore = useAccountInfoStore();
	const cerInfoStore = useCerInfoStore();
	// 驾校开通的培训类型
	const openedTrainTypesStore = useOpenedTrainTypesStore();

	const state = reactive<StateModel>({
		openLoading: false,
		visible: false,
		beforeCallback: null,
		callback: null
	});

	const methods = {
		async open({ beforeCallback, callback }: OpenModel = {}) {
			state.openLoading = true;

			methods
				.checkCanBuy()
				.then(canBuy => {
					typeof beforeCallback === 'function' && beforeCallback({ canBuy });
					if (canBuy) {
						state.callback = callback;
						state.visible = true;
					}
				})
				.catch(() => {
					beforeCallback({
						canBuy: false
					});
				})
				.finally(() => {
					state.openLoading = false;
				});
		},
		// 校验是否能购买元宝
		async checkCanBuy(): Promise<boolean> {
			// await new Promise((resolve, reject) => {
			// 	setTimeout(() => {
			// 		reject();
			// 	}, 1000);
			// });

			await Promise.all([
				certificationInfoStore.fetchCertificationInfo(true),
				userProfileStore.fetchUserProfile()
			]);
			if (cerInfoStore.data.value.userType === UserTypeEnum.AGENT) {
				MUtils.alert({
					title: '提示',
					content: '当前驾校为代理，无法使用此功能，请先联系客服人员'
				});
				return false;
			}
			if (!userProfileStore.isSuperAdmin) {
				MUtils.toast('请联系超级管理员充值元宝', MESSAGE_TYPE.error);
				return false;
			}

			const jiaxiaoConfig = await GetJiaxiaoConfigStore.request().getData();
			const { phoneClueService, basicClueService, uaClueService } = jiaxiaoConfig;

			if (!phoneClueService && !basicClueService && !uaClueService) {
				await MUtils.alert({
					title: '提示',
					content:
						'要至少成功开通一项线索服务才可充值元宝，如招生宝线索无法开通，可联系工作人员或申请开通电话线索服务',
					type: MESSAGE_TYPE.warning,
					btText: '去开通'
				});

				const targetId = openedTrainTypesStore.data.value.hasCar ? 'W14010105' : 'W14010301';
				PaasPostMessage.post('base://get.path-jump', {
					id: targetId,
					appName: 'lincoln',
					query: {
						tab: 'clueSetting'
					}
				});

				return false;
			}

			await Promise.all([
				blackListStore.getCoinIsBuyStore(),
				accountInfoStore.fetchCityRechargeCoinLimit(),
				accountInfoStore.fetchAccountAndCoinInfo()
			]);

			if (!accountInfoStore.goldArr.length) {
				MUtils.toast('未获取到元宝购买配置，详情请联系客服！', MESSAGE_TYPE.error);
				return false;
			}

			if (!accountInfoStore.data.coinRechargeAuth) {
				MUtils.alert({
					title: '提示',
					content:
						'<div style="text-align: center;line-height: 24px;font-size: 16px;">充值元宝, 尊享平台海量优质学员!<br/>把握红利期! 招生不再难!<br/>联系人: 杜经理 ***********</div>'
				});
				return false;
			}

			return true;
		}
	};

	return {
		...toRefs(state),
		...methods
	};
});
