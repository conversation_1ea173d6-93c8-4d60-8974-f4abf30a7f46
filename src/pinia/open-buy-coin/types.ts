import { AuthResultEnum } from '@/pinia/open-pay-account-auth/constants';
import { RechargeStatusEnum } from '@/pinia/recharge/constant';

import { ActionEnum } from '@/utils/constants/common';

export interface StateModel {
	openLoading: boolean;
	visible: boolean;
	beforeCallback?: (params: BuyCoinBeforeCallbackDataModel) => unknown;
	callback?: (params: BuyCoinCallbackDataModel) => unknown;
}

export interface BuyCoinBeforeCallbackDataModel {
	canBuy: boolean;
}

export interface BuyCoinCallbackDataModel {
	status?: RechargeStatusEnum;
	accountAuthStatus?: AuthResultEnum;
	action: ActionEnum;
	isClose: boolean;
}

export type OpenModel = Pick<StateModel, 'beforeCallback' | 'callback'>;
