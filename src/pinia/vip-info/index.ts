import { defineStore } from 'pinia';
import { NAME_MAP } from '@/pinia/store-name';
import { computed, reactive, toRefs } from 'vue';
import { GetMyVipStore } from '@/pinia/vip-info/store';
import { VipResponse } from '@/pinia/vip-info/types';
import { StateModel } from '@/pinia/types';
import { StatEnum } from '@/pinia/constant';
import dayjs from 'dayjs';

export const useVipInfoStore = defineStore(NAME_MAP.vipInfo, () => {
	const state = reactive<StateModel<VipResponse>>({
		stat: StatEnum.idle,
		data: null,
		error: null
	});

	const computeds = {
		isIdle: computed(() => state.stat === StatEnum.idle),
		isLoading: computed(() => state.stat === StatEnum.loading),
		isError: computed(() => state.stat === StatEnum.error),
		isSuccess: computed(() => state.stat === StatEnum.success),
		// 过期时长
		expiredDay: computed(() => {
			if (!state.data?.myVip?.vip) {
				// -1 代表没有开通过,不是-1 代表开通了，但过期
				if (state.data?.myVip?.lastExpiredTime !== -1) {
					return dayjs().diff(state.data?.myVip?.lastExpiredTime, 'day');
				}
			}
			return '';
		}),
		// 按钮文字
		btnText: computed(() => {
			let str = '';
			if (!state.data?.myVip?.vip) {
				// -1 代表没有开通过,不是-1 代表开通了，但过期
				if (state.data?.myVip?.lastExpiredTime !== -1) {
					str = '购买';
				} else {
					str = '开通';
				}
			} else {
				if (state.data?.myVip?.freeVip) {
					str = '购买';
				} else {
					if (state.data?.myVip?.lastExpiredTime - new Date().getTime() < 30 * 3600 * 24 * 1000) {
						// 如果在续费时间内
						str = '续费';
					} else if (~state.data?.myVip?.name.indexOf('黑金')) {
						// 黑金会员无法升级，隐藏按钮
						str = '';
					} else {
						str = '升级';
					}
				}
			}
			return str;
		}),
		// 会员名称
		name: computed(() => state.data?.myVip.name),
		// 会员年数
		age: computed(() => state.data?.myVip?.vipAge)
	};

	const methods = {
		fetchVipInfo: async (): Promise<unknown> => {
			try {
				state.stat = StatEnum.loading;
				state.data = await GetMyVipStore.request().getData();
				state.stat = StatEnum.success;
				return state.data;
			} catch (error) {
				state.stat = StatEnum.error;
				state.error = error.data;
				return error;
			}
		}
	};

	return {
		...toRefs(state),
		...computeds,
		...methods
	};
});
