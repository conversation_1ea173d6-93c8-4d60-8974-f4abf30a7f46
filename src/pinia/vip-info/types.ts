export interface VipResponse {
	myPrivilegeList: PrivilegeModel[]; //权限列表
	myVip: VipDetailModel; //vip信息详情
}

export interface PrivilegeModel {
	hasAuth: boolean; //是否有权限
	image?: string; //权限icon
	name: string; //权限名称
	code?: number; // icon的id
}

export interface VipDetailModel {
	expiredTime: number; //有效时间
	freeVip: boolean; //是否是免费vip
	lastExpiredTime: number; //最后的有效时间
	level: number; //vip等级
	// logo: string; //驾校logo
	name: string; //驾校名称
	recruitSiteLimit: number;
	smart: boolean; //是否是智慧驾校
	vip: boolean; //是否是付费vip
	vipAge: number; //vip年限
	// vipTag?: string; //vip图标
}
