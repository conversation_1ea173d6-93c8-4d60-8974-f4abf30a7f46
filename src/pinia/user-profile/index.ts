import { StatEnum } from '@/pinia/constant';
import { NAME_MAP } from '@/pinia/store-name';
import { StateModel } from '@/pinia/types';
import { PaasPostMessage } from '@paas/paas-library';
import { defineStore } from 'pinia';
import { MyInfoResponse } from './types';

export const useUserProfileStore = defineStore(NAME_MAP.userProfile, {
	state: (): StateModel<MyInfoResponse> => {
		return {
			stat: StatEnum.idle,
			data: null,
			error: null
		};
	},
	getters: {
		isIdle: (state): boolean => state.stat === StatEnum.idle,
		isLoading: (state): boolean => state.stat === StatEnum.loading,
		isError: (state): boolean => state.stat === StatEnum.error,
		isSuccess: (state): boolean => state.stat === StatEnum.success,
		isSuperAdmin: (state): boolean => state.data?.admin,
		isRecruitDirector: (state): boolean => {
			const myInfo = state.data;
			if (!myInfo.userTenants || !myInfo.userTenants.length) {
				return false;
			}
			const currentUserTenant = myInfo.userTenants.find(userTenant => userTenant.id === myInfo.tenantId);
			if (!currentUserTenant?.roleNames?.length) {
				return false;
			} else {
				return currentUserTenant.roleNames.some(roleName => roleName === '招生主管');
			}
		}
	},
	actions: {
		async fetchUserProfile(): Promise<unknown> {
			try {
				this.stat = StatEnum.loading;
				this.data = await PaasPostMessage.post('base://get.userProfile');
				this.stat = StatEnum.success;
			} catch (error) {
				this.stat = StatEnum.error;
				this.error = error;
				return error;
			}
		}
	}
});
