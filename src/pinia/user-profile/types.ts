export interface MyInfoResponse {
	admin: boolean;
	avatar: string;
	birthday: string;
	defaultCampusId: number;
	defaultRecruitSiteId: number;
	enterpriseId: number;
	gender: string;
	jiaxiaoId: number;
	joinDate: string;
	name: string;
	tenantId: number;
	tenantName: string;
	tenantRegisterTime: number;
	userId: string;
	userTenants: userTenantModel[];
}

interface userTenantModel {
	cityCode: string;
	cityName: string;
	id: number;
	name: string;
	roleNames: string[];
	userName: string;
}
