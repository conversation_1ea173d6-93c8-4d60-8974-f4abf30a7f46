import { BizSceneEnum, PaySubTypeEnum, RechargeStatusEnum, SourceEnum } from './constant';
import { PayTypeEnum } from '@/components/pay-type/constants';
import { StateModel } from '@/pinia/types';

export interface RechageStateModel extends StateModel<RechargeResponse> {
	// 订单状态
	status: RechargeStatusEnum;
	// 订单状态loading
	statusLoading: boolean;
	// 订单是否超时
	isTimeout: boolean;
}

export interface RechargeModel {
	// 金额
	amount: number | string;
	// 支付子类型
	paySubType: PaySubTypeEnum;
	// 微信openId
	openId?: string;
	// 支付宝前端回调地址
	alipayReturnUrl?: string;
	// 交易来源
	source: SourceEnum;
	// 交易场景
	bizScene: BizSceneEnum;
	// 商品编码(购买商品场景必传)
	goodsCode?: string;
	// 短信数量
	smsCount?: number;
	// 元宝数量
	coinCount?: number;
}

export interface RechargeResponse {
	// 订单号
	orderNumber: string;
	// 支付参数
	content: string;
	// 事件id
	eventId: string;
}

export interface StatusModel {
	// 订单号
	orderNumber: string;
	// 支付方式
	payType?: PayTypeEnum;
	// 是否开启5分钟超时
	openTimeout?: boolean;
}

// 获取订单状态
export type StatusResponse = RechargeStatusEnum;
