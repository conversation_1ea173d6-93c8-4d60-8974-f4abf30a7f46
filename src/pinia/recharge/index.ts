import { PayTypeEnum } from '@/components/pay-type/constants';
import { useAccountInfoStore } from '@/pinia/account-info';
import { StatEnum } from '@/pinia/constant';
import { BizSceneEnum, PaySubTypeEnum, RechargeStatusEnum } from '@/pinia/recharge/constant';
import { AccountRechargeStore, CheckStatusStore } from '@/pinia/recharge/store';
import { MESSAGE_TYPE, MUtils } from '@paas/paas-library';
import { defineStore } from 'pinia';
import { computed, reactive, toRefs } from 'vue';
import { NAME_MAP } from '../store-name';
import { RechageStateModel, RechargeModel, StatusModel } from './types';

// 轮询订单状态的定时器
let statusTimer: NodeJS.Timer = null;
// 5分钟订单超时的定时器
let orderTimeoutTimer: NodeJS.Timer = null;

export const useRechargeStore = defineStore(NAME_MAP.recharge, () => {
	const accountInfoStore = useAccountInfoStore();

	const state = reactive<RechageStateModel>({
		stat: StatEnum.idle,
		// 订单数据
		data: null,
		error: null,
		// 订单状态
		status: RechargeStatusEnum.ING,
		// 是否在轮询
		statusLoading: false,
		// 订单是否超时
		isTimeout: false
	});

	const computeds = {
		isIdle: computed(() => state.stat === StatEnum.idle),
		isLoading: computed(() => state.stat === StatEnum.loading),
		isError: computed(() => state.stat === StatEnum.error),
		isSuccess: computed(() => state.stat === StatEnum.success)
	};

	const methods = {
		// 清除全部定时器
		clearTimer: (): void => {
			state.statusLoading = false;
			if (statusTimer) {
				clearInterval(statusTimer);
				statusTimer = null;
			}
			if (orderTimeoutTimer) {
				clearTimeout(orderTimeoutTimer);
				orderTimeoutTimer = null;
			}
		},
		// 单次获取订单状态
		checkOrderStatus: (params: StatusModel): Promise<void> => {
			return new Promise(resolve => {
				CheckStatusStore.request(params)
					.getData()
					.then(data => {
						state.status = data;
					})
					.catch(() => {
						state.status = RechargeStatusEnum.FAIL;
					})
					.finally(() => {
						resolve();
					});
			});
		},
		// 充值
		fetchRecharge: async (params: RechargeModel): Promise<unknown> => {
			try {
				state.stat = StatEnum.loading;

				// 如果是对公转账充值，则直接获取驾校账户信息
				if (params.bizScene === BizSceneEnum.ACCOUNT_RECHARGE && params.paySubType === PaySubTypeEnum.CMB) {
					state.data = null;
					await accountInfoStore.fetchAccountInfo();
					state.stat = StatEnum.success;
					return null;
				}

				// 否则创建充值订单
				state.data = await AccountRechargeStore.request({
					request: JSON.stringify(params)
				}).getData();
				state.stat = StatEnum.success;
				return state.data;
			} catch (error) {
				state.stat = StatEnum.error;
				state.error = error.data;
				return error;
			}
		},
		// 获取订单状态轮询
		fetchStatus: (params: StatusModel): Promise<RechargeStatusEnum> => {
			methods.clearTimer();
			state.statusLoading = true;

			return new Promise(resolve => {
				statusTimer = setInterval(async () => {
					await methods.checkOrderStatus(params);
					if ([RechargeStatusEnum.SUCCESS, RechargeStatusEnum.FAIL].includes(state.status)) {
						methods.clearTimer();
						resolve(state.status);
					}
				}, 3000);

				if (params.openTimeout) {
					orderTimeoutTimer = setTimeout(() => {
						methods.clearTimer();
						if (params.payType === PayTypeEnum.BALANCE) {
							// 如果是余额支付，直接toast
							MUtils.toast('获取充值信息失败，请稍后重试', MESSAGE_TYPE.warning);
						} else {
							// 微信、支付宝 支付，展示超时提醒
							state.isTimeout = true;
						}
						resolve(RechargeStatusEnum.ING);
					}, 5 * 60 * 1000);
				}
			});
		}
	};

	return {
		...toRefs(state),
		...computeds,
		...methods
	};
});
