import { computed, reactive, toRefs } from 'vue';
import { defineStore } from 'pinia';
import { NAME_MAP } from '@/pinia/store-name';
import { AccountInfoStateModel } from '@/pinia/account-info/types';
import { GetAccountAndCoinInfoStore, GetAccountInfoStore, GetCoinGoodsInfoStore } from '@/pinia/account-info/store';
import { StatEnum } from '@/pinia/constant';

export const useAccountInfoStore = defineStore(NAME_MAP.accountInfo, () => {
	const state = reactive<AccountInfoStateModel>({
		// 余额及元宝信息
		stat: StatEnum.idle,
		data: null,
		error: null,
		// 账户信息
		accountStat: StatEnum.idle,
		accountData: null,
		accountError: null,
		// 元宝购买配置
		goldArr: []
	});

	const computeds = {
		// 余额及元宝信息
		isIdle: computed(() => state.stat === StatEnum.idle),
		isLoading: computed(() => state.stat === StatEnum.loading),
		isError: computed(() => state.stat === StatEnum.error),
		isSuccess: computed(() => state.stat === StatEnum.success),
		// 元宝余额
		coinAmount: computed(() => state.data?.coinAmount),
		// 账户余额
		accountAmount: computed(() => state.data?.accountAmount),
		// 元宝单价
		coinPrice: computed(() => state.data?.coinPrice),
		// 账户信息
		isAccountIdle: computed(() => state.accountStat === StatEnum.idle),
		isAccountLoading: computed(() => state.accountStat === StatEnum.loading),
		isAccountError: computed(() => state.accountStat === StatEnum.error),
		isAccountSuccess: computed(() => state.accountStat === StatEnum.success)
	};

	const methods = {
		// 获取账户及元宝余额信息
		async fetchAccountAndCoinInfo(): Promise<unknown> {
			if (state.stat === StatEnum.loading) {
				return;
			}
			try {
				state.stat = StatEnum.loading;
				state.data = await GetAccountAndCoinInfoStore.request().getData();
				state.stat = StatEnum.success;
				return state.data;
			} catch (error) {
				state.stat = StatEnum.error;
				state.error = error.data;
				return error;
			}
		},
		// 获取驾校账户信息
		async fetchAccountInfo(): Promise<unknown> {
			if (state.accountStat === StatEnum.loading) {
				return;
			}
			try {
				state.accountStat = StatEnum.loading;
				state.accountData = await GetAccountInfoStore.request().getData();
				state.accountStat = StatEnum.success;
				return state.data;
			} catch (error) {
				state.accountStat = StatEnum.error;
				state.accountError = error.data;
				return error;
			}
		},
		// 获取元宝购买配置
		async fetchCityRechargeCoinLimit() {
			const data = await GetCoinGoodsInfoStore.request().getData();

			state.goldArr = data?.itemList || [];
		}
	};

	return {
		...toRefs(state),
		...computeds,
		...methods
	};
});
