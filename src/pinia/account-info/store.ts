import { AccountAndCoinInfoStore, AccountInfoStore, CoinGoodsInfo } from '@/store/jiaxiao-vip/web';
import { AccountAndCoinInfoResponse, AccountInfoResponse, CoinGoodsInfoResponse } from './types';
import { PaasListResponse } from '@paas/paas-library';

// 获取驾校的元宝及账户余额信息
export const GetAccountAndCoinInfoStore = new AccountAndCoinInfoStore<AccountAndCoinInfoResponse>({});

// 获取驾校账户信息
export const GetAccountInfoStore = new AccountInfoStore<AccountInfoResponse>({});

// 元宝购买配置
export const GetCoinGoodsInfoStore = new CoinGoodsInfo<PaasListResponse<CoinGoodsInfoResponse>>({});
