import { StateModel } from '@/pinia/types';
import { StatEnum } from '@/pinia/constant';

// 余额及元宝信息
export interface AccountAndCoinInfoResponse {
	// 账户余额
	accountAmount: number;
	// 元宝单价，无用字段
	coin: number;
	// 元宝余额
	coinAmount: string;
	// 元宝单价
	coinPrice: number;
	// 能否充值
	coinRechargeAuth: boolean;
	// 不能充值的提示，目前无用
	comment: string;
	// 是否有充值记录
	hasRechargeRecord: boolean;
}

// 驾校账户信息
export interface AccountInfoResponse {
	amount: number;
	cmbMcAccountName: string;
	cmbMcAccountNo: string;
	cmbMcBankName: string;
	cmbTradeCode: string;
	contactUserName: string;
	customerId: number;
	customerName: string;
	frozenAmount: number;
	groupId: number;
	id: number;
}

export interface AccountInfoStateModel extends StateModel<AccountAndCoinInfoResponse> {
	accountStat: StatEnum;
	accountData: AccountInfoResponse | null;
	accountError: Error | null;
	goldArr: CoinGoodsInfoResponse[];
}

export interface CoinGoodsInfoResponse {
	/** 商品code */
	goodsCode: string;

	/** 商品名称 */
	goodsName: string;

	/** 商品价格 */
	goodsPrice: number;

	/** 元宝个数 */
	coinCount: number;
}
