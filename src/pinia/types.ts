import { ComputedRef } from 'vue';

import { StatEnum } from '@/pinia/constant';

export interface StateModel<D> {
	error: Error | null;
	data: D | null;
	stat: StatEnum;
}

export interface RequestStatusModel {
	isIdle: boolean;
	isLoading: boolean;
	isError: boolean;
	isSuccess: boolean;
}

export interface RequestStatusComputedModel {
	isIdle: ComputedRef<boolean>;
	isLoading: ComputedRef<boolean>;
	isError: ComputedRef<boolean>;
	isSuccess: ComputedRef<boolean>;
}
