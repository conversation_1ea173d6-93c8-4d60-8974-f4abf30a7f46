import { computed, reactive, toRefs } from 'vue';
import { defineStore } from 'pinia';
import { NAME_MAP } from '@/pinia/store-name';
import { GetCoinIsBuyStore } from '@/pinia/black-list/store';
import { CoinIsBuyResponse } from '@/pinia/black-list/types';
import { MESSAGE_TYPE, MUtils } from '@paas/paas-library';
import { StateModel } from '@/pinia/types';
import { StatEnum } from '@/pinia/constant';

export const useBlackListStore = defineStore(NAME_MAP.blackList, () => {
	const state = reactive<StateModel<CoinIsBuyResponse>>({
		stat: StatEnum.idle,
		data: null,
		error: null
	});

	const computeds = {
		// 余额及元宝信息
		isIdle: computed(() => state.stat === StatEnum.idle),
		isLoading: computed(() => state.stat === StatEnum.loading),
		isError: computed(() => state.stat === StatEnum.error),
		isSuccess: computed(() => state.stat === StatEnum.success)
	};

	const methods = {
		// 获取是否能购买
		async getCoinIsBuyStore(): Promise<unknown> {
			if (state.stat === StatEnum.loading) {
				return;
			}
			try {
				state.stat = StatEnum.loading;
				state.data = await GetCoinIsBuyStore.request().getData();
				state.stat = StatEnum.success;
				return state.data;
			} catch (error) {
				state.stat = StatEnum.error;
				state.error = error.data;
				return error;
			}
		},
		checkBuy() {
			if (!state.data.value) {
				MUtils.alert({
					title: '提示',
					content: `<div>
          <div style="font-size: 16px; color: #212121">当前服务已暂停，如需使用，请联系客服！</div>
          <img style="width: 150px; margin-top: 14px" src="https://web-resource.mc-cdn.cn/web/qiye-jkbd-v3/business/black_list_wx_code.png!300x0" alt="" />
          <div style="font-size: 14px; color: #333333;">扫码联系客服</div>
          </div>`,
					type: MESSAGE_TYPE.error
				});
			}
			return state.data.value;
		}
	};

	return {
		...toRefs(state),
		...computeds,
		...methods
	};
});
