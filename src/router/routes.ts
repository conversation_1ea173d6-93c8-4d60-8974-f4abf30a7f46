export const routes = [
	{
		path: '/goods-center',
		name: 'goods-center',
		component: () => import('@/application/goods-center/index.vue')
	},
	{
		path: '/firm-record',
		name: 'firm-record',
		component: () => import('@/application/firm-record/index.vue')
	},
	{
		path: '/gold-balance-detail',
		name: 'gold-balance-detail',
		component: () => import('@/application/gold-balance-detail/index.vue')
	},
	{
		path: '/sms-rights-package',
		name: 'sms-rights-package',
		component: () => import('@/application/sms-rights-package/index.vue')
	},
	{
		path: '/sms-rights-package-detail',
		name: 'sms-rights-package-detail',
		component: () => import('@/application/sms-rights-package-detail/index.vue')
	},
	{
		path: '/phone-rights-package',
		name: 'phone-rights-package',
		component: () => import('@/application/phone-rights-package/index.vue')
	},
	{
		path: '/phone-rights-package-detail',
		name: 'phone-rights-package-detail',
		component: () => import('@/application/phone-rights-package-detail/index.vue')
	},
	{
		path: '/distribution-for-gold',
		name: 'distribution-for-gold',
		component: () => import('@/application/distribution-for-gold/index.vue')
	},
	{
		path: '/distribution-for-gold-detail',
		name: 'distribution-for-gold-detail',
		component: () => import('@/application/distribution-for-gold-detail/index.vue')
	},
	{
		path: '/vip-rights-detail',
		name: 'vip-rights-detail',
		component: () => import('@/application/vip-rights-detail/index.vue')
	},
	{
		path: '/batch-order-list',
		name: 'batch-order-list',
		component: () => import('@/application/batch-order-list/index.vue')
	},
	{
		path: '/pay-account-auth-list',
		name: 'pay-account-auth-list',
		component: () => import('@/application/pay-account-auth-list/index.vue')
	},
	{
		path: '/buy-coin',
		name: 'buy-coin',
		component: () => import('@/application/buy-coin/index.vue'),
		meta: {
			wrapperStyle: {
				margin: 0,
				height: '100%',
				backgroundColor: 'transparent'
			},
			appStyle: {
				backgroundColor: 'transparent'
			}
		}
	},
	{
		path: '/recharge',
		name: 'recharge',
		component: () => import('@/application/recharge/index.vue'),
		meta: {
			wrapperStyle: {
				margin: 0,
				height: '100%',
				backgroundColor: 'transparent'
			},
			appStyle: {
				backgroundColor: 'transparent'
			}
		}
	},
	{
		path: '/account-auth',
		name: 'account-auth',
		component: () => import('@/application/account-auth/index.vue'),
		meta: {
			wrapperStyle: {
				margin: 0,
				height: '100%',
				backgroundColor: 'transparent'
			},
			appStyle: {
				backgroundColor: 'transparent'
			}
		}
	},
	{
		path: '/invoice-management',
		name: 'invoice-management',
		component: () => import('@/application/invoice-management/index.vue')
	}
];
