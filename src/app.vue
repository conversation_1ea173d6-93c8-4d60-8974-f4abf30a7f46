<template>
	<pm-app-wrapper>
		<pm-router-view :router="router"></pm-router-view>

		<!-- 支付授权弹窗 -->
		<pay-account-auth-dialog-comp />
		<!-- 图片预览 -->
		<preview-img-comp />
	</pm-app-wrapper>
</template>

<script lang="ts">
import PayAccountAuthDialogComp from '@/components/pay-account-auth-dialog/index.vue';
import { useUserProfileStore } from '@/pinia/user-profile';
import emitter, { EmitterEnum } from '@/utils/bus';
import { PaasPostMessage } from '@paas/paas-library';
import { defineComponent, onMounted } from 'vue';
import router from './router';
import PreviewImgComp from '@/components/preview-img/index.vue';

export default defineComponent({
	components: {
		PayAccountAuthDialogComp,
		PreviewImgComp
	},
	setup() {
		const userProfileStore = useUserProfileStore();

		const methods = {
			registerViewShow() {
				// 由于在组件内注册view.show，切换页面时会导致重复注册，所以统一在app内注册，然后bus抛出
				PaasPostMessage.register('view.show', data => {
					console.log('buick, view.show');

					emitter.emit(EmitterEnum.VIEW_SHOW, data);
				});
			},
			registerViewHide() {
				PaasPostMessage.register('view.hide', data => {
					emitter.emit(EmitterEnum.VIEW_HIDE, data);
				});
			}
		};

		onMounted(() => {
			userProfileStore.fetchUserProfile();
			methods.registerViewShow();
			methods.registerViewHide();
		});

		return {
			router
		};
	}
});
</script>

<style lang="less"></style>
