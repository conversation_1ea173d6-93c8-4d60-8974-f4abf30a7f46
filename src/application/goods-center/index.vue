<template>
	<div class="app-container goods-center">
		<div class="title">
			商城中心

			<div class="right-btn" v-if="displayList.length">
				<div class="btn" v-if="show" @click="handleChange(false)">
					收起
					<UpOutlined />
				</div>
				<div class="btn" v-else @click="handleChange(true)">
					展开
					<DownOutlined />
				</div>
			</div>
		</div>

		<div v-if="displayList.length">
			<top-carousel-comp v-show="show" :list="displayList" />
		</div>

		<the-stat-comp />
		<account-detail-comp />

		<batch-buy-dialog-comp ref="batchBuyDialogRef" />
	</div>
</template>

<script lang="ts" src="./index.ts"></script>
<style lang="less" src="./index.less" scoped></style>
