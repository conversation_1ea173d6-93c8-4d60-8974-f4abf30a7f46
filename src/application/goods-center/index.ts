import { DownOutlined, UpOutlined } from '@ant-design/icons-vue';
import AccountDetailComp from '@/application/goods-center/comps/account-detail/index.vue';
import BatchBuyDialogComp from '@/application/goods-center/comps/batch-buy-dialog/index.vue';
import { MUtils } from '@paas/paas-library';
import { defineComponent, onMounted, ref, reactive, toRefs } from 'vue';
import TheStatComp from './comps/the-stat/index.vue';
import TopCarouselComp from './comps/top-carousel/index.vue';
import { CASCADER_SHOW, CASCADER_EXPIRED_TIME } from '@/utils/storage-key';
import { GetDisplayStore } from './store';
import { StateModel } from './types';

export default defineComponent({
	components: {
		TheStatComp,
		AccountDetailComp,
		BatchBuyDialogComp,
		DownOutlined,
		UpOutlined,
		TopCarouselComp
	},
	setup() {
		const state = reactive<StateModel>({
			show: true,
			displayList: []
		});

		const components = {
			batchBuyDialogRef: ref<InstanceType<typeof BatchBuyDialogComp>>(null)
		};

		// 方法
		const methods = {
			init() {
				components.batchBuyDialogRef.value.checkShow();
				methods.getDisplayList();
			},

			handleChange(show: boolean): void {
				state.show = show;
				MUtils.setStorage(CASCADER_SHOW, show);
			},

			// 展示位列表
			getDisplayList(): void {
				GetDisplayStore.request({
					name: '商场中心页面'
				})
					.getData()
					.then(res => {
						state.displayList = res.itemList || [];
						const EXPIRED_TIME = MUtils.getStorage(CASCADER_EXPIRED_TIME);
						const nowTime = new Date().getTime();
						// 7天清除CASCADER_SHOW的缓存
						if (!EXPIRED_TIME || (EXPIRED_TIME && nowTime - EXPIRED_TIME > 1000 * 60 * 60 * 24 * 7)) {
							MUtils.setStorage(CASCADER_EXPIRED_TIME, nowTime);
							MUtils.removeStorage(CASCADER_SHOW);
						}
						const show = MUtils.getStorage(CASCADER_SHOW);
						state.show = show === null ? true : show;
					})
					.catch(() => {
						state.displayList = [];
					});
			}
		};

		onMounted(() => {
			MUtils.sendTongji(['展示', '商城中心', '商城中心']);

			methods.init();
		});

		return {
			...toRefs(state),
			...components,
			...methods
		};
	}
});
