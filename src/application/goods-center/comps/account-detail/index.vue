<template>
	<pm-effi :controller="controller">
		<pm-search>
			<pm-search-single
				:span="8"
				:antd-props="{ placeholder: ['交易开始时间', '交易结束时间'] }"
				data-index="startTime|endTime"
				xtype="RANGEPICKER"
			/>
		</pm-search>

		<pm-table
			:columns="COLUMNS"
			:useCustomColumn="true"
			:showCustom="true"
			:helpIcon="false"
			:operations-width="100"
			:operations-fixed="true"
		>
			<template #beforeButtons>
				<payment-agreement-comp v-if="canShowInvoiceBtn" tip="" />
			</template>

			<template #beforeCustom>
				<m-button v-if="canShowInvoiceBtn" type="primary" class="mr-10" @click="onJumpInvoiceManagement">
					去开票
				</m-button>

				<confirm-button
					v-if="userProfileStore.isSuperAdmin"
					:loading="exportLoading"
					ghost
					type="primary"
					@click="onExport"
				>
					导出
				</confirm-button>
				<m-button type="primary" class="ml-10" @click="onJumpFirmRecord">对公转账记录</m-button>
			</template>

			<template #certifiedStatus="{ record }">
				<m-tag v-if="record.certifiedStatus !== null" :color="getTypeRender(record).color">
					{{ getTypeRender(record).dsc }}
				</m-tag>
			</template>

			<template #operations="{ record }">
				<div class="operation-content">
					<m-button
						type="link"
						v-if="record.certifiedStatus !== null"
						:disabled="[StatusEnum.AUTH, StatusEnum.SUCCESS].includes(record.certifiedStatus)"
						@click="onAuth(record)"
					>
						去授权
					</m-button>
				</div>
			</template>
		</pm-table>
	</pm-effi>
</template>

<script lang="ts" src="./index.ts"></script>
<style lang="less" src="./index.less" scoped></style>
