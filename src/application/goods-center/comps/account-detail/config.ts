import { ColumnXtype, MoneyUnit, TableColumn, TableDateFormat } from '@paas/paas-library';

export function getTableColumn(): TableColumn[] {
	return [
		{
			title: '时间',
			dataIndex: 'createTime',
			dateFormat: TableDateFormat.MINUTES,
			width: 100
		},
		{
			title: '商品',
			dataIndex: 'goodsTypeName',
			width: 200
		},
		{
			title: '交易类型',
			dataIndex: 'typeName',
			width: 100
		},
		{
			title: '交易金额',
			dataIndex: 'money',
			xtype: ColumnXtype.MONEY,
			moneyConfig: {
				unit: MoneyUnit.YUAN
			},
			width: 140
		},
		{
			title: '账户余额',
			dataIndex: 'afterMoney',
			xtype: ColumnXtype.MONEY,
			moneyConfig: {
				unit: MoneyUnit.YUAN
			},
			width: 140
		},
		{
			title: '支付方式',
			dataIndex: 'payTypeName',
			useConfigWidth: false,
			width: 100
		},
		{
			title: '操作人员',
			dataIndex: 'createUserName',
			useConfigWidth: false,
			online: true,
			width: 150
		},
		{
			title: '支付账户',
			dataIndex: 'account',
			width: 150
		},
		{
			title: '授权状态',
			dataIndex: 'certifiedStatus',
			xtype: ColumnXtype.CUSTOM,
			width: 155
		}
	];
}
