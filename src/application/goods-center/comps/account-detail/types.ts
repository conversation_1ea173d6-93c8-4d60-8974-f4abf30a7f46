import { StatusEnum } from '@/components/pay-account-auth-batch/constants';

export interface StateModel {
	exportLoading: boolean;
	canShowInvoiceBtn: boolean;
}

export interface AccountDetailResponse {
	afterMoney: string;
	createTime: number;
	createUserName: string;
	goodsTypeName: string;
	money: string;
	payTypeName: string;
	typeName: string;
	payNumber: string;
	certifiedStatus: StatusEnum;
	account: string;
}
