import { getTableColumn } from '@/application/goods-center/comps/account-detail/config';
import { GetAccountDetailListExportStore, GetAccountDetailListStore, GetIsShowInvoiceButtonStore } from './store';

import { STATUS_LIST, StatusEnum } from '@/components/pay-account-auth-batch/constants';
import { ColorEnum } from '@/components/text-color/constants';
import { useOpenPayAccountAuthStore } from '@/pinia/open-pay-account-auth';
import { useUserProfileStore } from '@/pinia/user-profile';
import { ConfirmButton } from '@paas/paas-base-lib';
import { MESSAGE_TYPE, ModelController, MUtils, PaasPostMessage } from '@paas/paas-library';
import { defineComponent, reactive, toRefs } from 'vue';
import { AccountDetailResponse, StateModel } from './types';
import PaymentAgreementComp from '@/components/payment-agreement/index.vue';

export default defineComponent({
	components: {
		ConfirmButton,
		PaymentAgreementComp
	},
	setup() {
		const userProfileStore = useUserProfileStore();
		const openPayAccountAuthStore = useOpenPayAccountAuthStore();

		const state = reactive<StateModel>({
			exportLoading: false,

			canShowInvoiceBtn: false
		});

		const controller = new ModelController({
			table: {
				store: GetAccountDetailListStore
			}
		});

		const constants = {
			StatusEnum,
			COLUMNS: getTableColumn()
		};

		const methods = {
			async init() {
				const result = await GetIsShowInvoiceButtonStore.request().getData();

				state.canShowInvoiceBtn = userProfileStore.isSuperAdmin && result.value;
			},

			// 授权弹窗
			onAuth(lineData: AccountDetailResponse) {
				openPayAccountAuthStore.accountIdOpen({
					typeShow: lineData.payTypeName,
					account: lineData.account,
					payNumber: lineData.payNumber,
					callback: () => {
						controller.tableRequest();
					}
				});
			},
			getTypeRender(lineData: AccountDetailResponse) {
				const item = STATUS_LIST.find(v => v.value === lineData.certifiedStatus);

				return {
					color: item?.color || ColorEnum.RED,
					dsc: item?.label || ''
				};
			},
			onExport() {
				const params = {
					export: true,
					fileName: '消费明细.xls',
					...MUtils.filterPagingParams(controller.table.getPrevParams())
				};

				state.exportLoading = true;
				GetAccountDetailListExportStore.request(params)
					.getData()
					.finally(() => {
						setTimeout(() => {
							state.exportLoading = false;
						}, 5000);
					});
			},
			// 跳转对公转账记录
			onJumpFirmRecord() {
				PaasPostMessage.post('base://get.path-jump', {
					id: 'H3203',
					appName: 'buick'
				});
			},

			// 跳转开票
			onJumpInvoiceManagement() {
				if (!userProfileStore.isSuperAdmin) {
					MUtils.toast('账号无权限，请联系管理员', MESSAGE_TYPE.error);
					return;
				}

				PaasPostMessage.post('base://get.path-jump', {
					id: 'H3212',
					appName: 'buick'
				});
			}
		};

		methods.init();

		return {
			userProfileStore,
			controller,
			...toRefs(state),
			...constants,
			...methods
		};
	}
});
