import { AccountDetailListStore } from '@/store/jiaxiao-vip/web';
import { AccountDetailResponse } from '@/application/goods-center/comps/account-detail/types';
import { PaasListResponse } from '@paas/paas-library';
import { IsShowInvoiceButtonStore } from '@/store/jiaxiao-vip/invoice';

export const GetAccountDetailListStore = new AccountDetailListStore<PaasListResponse<AccountDetailResponse>>({});

export const GetAccountDetailListExportStore = new AccountDetailListStore<PaasListResponse<AccountDetailResponse>>({
	method: 'ATAG_GET'
});

/** 获取是否显示开票按钮 */
export const GetIsShowInvoiceButtonStore = new IsShowInvoiceButtonStore<{ value: boolean }>({});
