<template>
	<div class="top-carousel">
		<div class="carousel-content">
			<m-carousel autoplay :autoplaySpeed="5000" arrows>
				<template #prevArrow>
					<div class="custom-slick-arrow" style="left: 10px; z-index: 1">
						<left-circle-outlined />
					</div>
				</template>
				<template #nextArrow>
					<div class="custom-slick-arrow" style="right: 10px">
						<right-circle-outlined />
					</div>
				</template>
				<div
					class="img-item"
					:class="item.jumpUrl ? 'pointer' : ''"
					v-for="(item, index) in list"
					:key="index"
					@click="openNewPage(item.jumpUrl)"
				>
					<div class="item" :style="{ backgroundImage: `url(${item.materialUrl})` }"></div>
				</div>
			</m-carousel>
		</div>
	</div>
</template>

<script lang="ts" src="./index.ts"></script>
<style lang="less" src="./index.less" scoped></style>
