import { defineComponent, toRefs, reactive, PropType } from 'vue';
import { LeftCircleOutlined, RightCircleOutlined } from '@ant-design/icons-vue';
import { DisplayModel } from '../../types';

export default defineComponent({
	props: {
		list: {
			type: Array as PropType<DisplayModel[]>,
			default: () => []
		}
	},
	components: {
		LeftCircleOutlined,
		RightCircleOutlined
	},
	setup() {
		const state = reactive({});

		// methods
		const methods = {
			openNewPage(url?: string): void {
				if (url) {
					window.open(url, '_blank');
				}
			}
		};

		return {
			...toRefs(state),
			...methods
		};
	}
});
