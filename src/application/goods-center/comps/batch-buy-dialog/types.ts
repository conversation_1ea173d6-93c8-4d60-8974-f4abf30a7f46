export interface StateModel {
	visible: boolean;
	limit: string;
	notifyDetail: LeadBatchOrderNotifyResponse & Partial<LeadBatchNotifyPriceResponse>;
	loadingButton: boolean;
	loadingPrice: boolean;
	changeLimitTag: boolean;
}

export interface LeadBatchOrderNotifyResponse {
	activeEndTime: number;
	area: string;
	cheapAmount: number;
	cheapCoin: number;
	driveLicenseType: string;
	endTime: number;
	limit: number;
	minDiscountLimit: number;
	orderId: number;
	startTime: number;
	realSaleAmount: number;
	realSaleCoin: number;
}

export interface LeadBatchNotifyPriceResponse {
	// 实际售卖元宝
	realSaleCoin: number;
	// 实际售卖价格（单位元）
	realSaleAmount: number;
	// 比商城购买优惠元宝
	cheapCoin: number;
	// 比商城购买优惠价格（单位元）
	cheapAmount: number;
	// 可购买线索数
	validLeadCount: number;
}
