import {
	FetchLeadBatchPayByNotifyStore,
	GetLeadBatchNotifyPriceStore,
	GetLeadBatchOrderNotifyStore
} from '@/application/goods-center/comps/batch-buy-dialog/store';
import { useAccountInfoStore } from '@/pinia/account-info';
import { useUserProfileStore } from '@/pinia/user-profile';
import { useOpenBuyCoinStore } from '@/pinia/open-buy-coin';
import { REG_NATURAL_NUMBER } from '@/utils/util-reg';
import { timeFilter } from '@/utils/utils';
import { MESSAGE_TYPE, MUtils, PaasPostMessage } from '@paas/paas-library';
import { defineComponent, reactive, toRefs, nextTick } from 'vue';
import { StateModel } from './types';
import { useOpenedTrainTypesStore } from '@/utils/query';

export default defineComponent({
	setup() {
		const accountInfoStore = useAccountInfoStore();
		const openBuyCoinStore = useOpenBuyCoinStore();
		const userProfileStore = useUserProfileStore();
		const openedTrainTypesStore = useOpenedTrainTypesStore();

		const state = reactive<StateModel>({
			visible: false,
			limit: '',
			notifyDetail: null,
			loadingButton: false,
			loadingPrice: false,
			changeLimitTag: false
		});
		let loadingGetNotify = false;

		// 方法
		const methods = {
			onChangeLimit() {
				state.changeLimitTag = true;
			},
			getPrice() {
				if (!REG_NATURAL_NUMBER.test(state.limit)) {
					MUtils.toast('购买条数只能输入正整数', MESSAGE_TYPE.error);
					return;
				}

				state.loadingPrice = true;
				GetLeadBatchNotifyPriceStore.request({
					id: state.notifyDetail.orderId,
					limit: state.limit
				})
					.getData()
					.then(data => {
						state.notifyDetail = {
							...state.notifyDetail,
							...data
						};
						MUtils.toast('刷新价格成功', MESSAGE_TYPE.success);
					})
					.finally(() => {
						state.loadingPrice = false;
						state.changeLimitTag = false;
					});
			},
			onBuy() {
				if (state.changeLimitTag) {
					return;
				}

				if (!REG_NATURAL_NUMBER.test(state.limit)) {
					MUtils.toast('购买条数只能输入正整数', MESSAGE_TYPE.error);
					return;
				}

				if (state.notifyDetail.validLeadCount === 0) {
					methods.onClose();
					MUtils.toast('当前活动太火爆，线索已全部抢购完，请下次及时来购买！', MESSAGE_TYPE.error);
					return;
				}

				const content = `您本次操作将批量购买${state.limit}条符合您接收需求的线索，合计支付${state.notifyDetail.realSaleCoin}元宝，购买后不支持退款，请您根据实际需要购买！`;
				MUtils.confirm({
					title: '温馨提示',
					content,
					type: MESSAGE_TYPE.success
				}).then(async bool => {
					if (!bool) {
						return;
					}

					state.loadingButton = true;
					await accountInfoStore.fetchAccountAndCoinInfo();
					if (accountInfoStore.isError) {
						MUtils.toast('获取账户余额失败，请稍后重试', MESSAGE_TYPE.error);
						return;
					}

					if (Number(accountInfoStore.coinAmount) < state.notifyDetail.realSaleCoin) {
						state.loadingButton = false;
						MUtils.toast('元宝余额不足', MESSAGE_TYPE.warning);
						openBuyCoinStore.open();
						return;
					}

					FetchLeadBatchPayByNotifyStore.request({
						id: state.notifyDetail.orderId,
						limit: state.limit
					})
						.getData()
						.then(data => {
							const value = data.value;
							if (value) {
								methods.onClose();
								MUtils.toast('购买提交成功，实际购买数据，会在站内信通知您！', MESSAGE_TYPE.success);
							}
						})
						.finally(() => {
							state.loadingButton = false;
						});
				});
			},
			async checkShow() {
				if (state.visible) {
					return;
				}

				if (!openedTrainTypesStore.isFetched.value || !openedTrainTypesStore.isSuccess.value) {
					await openedTrainTypesStore.suspense();
				}
				if (!openedTrainTypesStore.data.value.hasCar) {
					return;
				}

				if (loadingGetNotify) {
					return;
				}

				loadingGetNotify = true;

				GetLeadBatchOrderNotifyStore.request()
					.getData()
					.then(data => {
						// data = {
						// 	activeEndTime: 1678012385000,
						// 	area: '安徽省;澳门特别行政区;北京市;重庆市;福建省;甘肃省;',
						// 	cheapAmount: 856.8,
						// 	cheapCoin: 85.68,
						// 	driveLicenseType: 'A1,A2,A3,B1,B2,C1,C2,C3,C4,C5,C6',
						// 	endTime: 1677945599000,
						// 	limit: 30,
						// 	minDiscountLimit: 2,
						// 	orderId: 1193,
						// 	startTime: 1677600000000,
						// 	realSaleAmount: 214.2,
						// 	realSaleCoin: 21.42
						// };

						state.notifyDetail = data;
						state.limit = data?.limit ? String(data.limit) : '';
						state.visible = !!data;
					})
					.finally(() => {
						loadingGetNotify = false;
					});
			},
			onClose() {
				state.visible = false;
				state.limit = '';
				state.notifyDetail = null;
				state.loadingButton = false;
				state.loadingPrice = false;
				state.changeLimitTag = false;
			},
			async onBuyClue() {
				if (!userProfileStore.isSuperAdmin && !userProfileStore.isRecruitDirector) {
					MUtils.toast('员工账号无权限购买，请联系管理员', MESSAGE_TYPE.error);
					return;
				}

				methods.onClose();
				await nextTick();
				PaasPostMessage.post('base://get.path-jump', {
					id: '********5',
					appName: 'lincoln',
					query: {
						tab: 'clueStore'
					}
				});
			}
		};

		// methods.checkShow();

		return {
			timeFilter,
			...toRefs(state),
			...methods
		};
	}
});
