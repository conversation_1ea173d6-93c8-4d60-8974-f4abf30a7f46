import { LeadBatchNotifyPriceStore, LeadBatchOrderNotifyStore, LeadBatchPayByNotifyStore } from '@/store/volvo/h5';
import {
	LeadBatchNotifyPriceResponse,
	LeadBatchOrderNotifyResponse
} from '@/application/goods-center/comps/batch-buy-dialog/types';

export const GetLeadBatchOrderNotifyStore = new LeadBatchOrderNotifyStore<LeadBatchOrderNotifyResponse>({});

export const GetLeadBatchNotifyPriceStore = new LeadBatchNotifyPriceStore<LeadBatchNotifyPriceResponse>({});

export const FetchLeadBatchPayByNotifyStore = new LeadBatchPayByNotifyStore<{ value: boolean }>({});
