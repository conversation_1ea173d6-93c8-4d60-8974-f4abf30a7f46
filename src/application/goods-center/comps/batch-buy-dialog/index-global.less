.goods-center-batch-buy-dialog-wrap {
	.ant-modal-content {
		border-radius: 4px;

		.ant-modal-body {
			.m-dialog-body {
				overflow: visible;
			}
		}
	}

	.content {
		position: relative;

		.banner {
			width: 560px;
			height: 100px;
			background: url('./img/banner.png') center no-repeat;
			background-size: cover;
			border-radius: 4px 4px 0 0;
		}

		.container {
			padding: 20px 42px 25px;
			border-bottom: 1px solid rgba(0, 0, 0, 0.09);

			h3 {
				font-size: 16px;
				font-weight: bold;
				text-align: center;
				color: #212121;
				line-height: 30px;

				span {
					color: var(--ant-error-color);
				}
			}

			.info-list {
				margin-top: 25px;

				& > li {
					font-size: 14px;
					font-weight: 400;
					text-align: left;
					color: #212121;
					line-height: 20px;
					margin-bottom: 16px;

					&:last-child {
						margin-bottom: 0;
					}

					.ant-input {
						width: 49px;
						height: 32px;
					}

					.gary {
						color: var(--text-secondary);
					}

					.warning {
						color: var(--ant-warning-color);
					}

					.red {
						color: var(--ant-error-color);

						.big {
							font-size: 20px;
							line-height: 28px;
						}
					}

					.orange {
						color: var(--ant-warning-color);
					}
				}
			}
		}

		.bottom {
			padding: 20px 160px;

			.ant-btn {
				width: 240px;
				height: 50px;
				border-radius: 4px;
				font-size: 20px;
				font-weight: 400;
				color: #ffffff;
				line-height: 28px;
			}
		}

		.close {
			width: 24px;
			height: 24px;
			background: url('./img/close.png') center no-repeat;
			background-size: cover;
			position: absolute;
			left: 50%;
			bottom: -44px;
			transform: translateX(-50%);
			cursor: pointer;
		}
	}
}
