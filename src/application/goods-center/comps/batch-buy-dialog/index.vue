<template>
	<pm-dialog
		:bodyPadding="false"
		:keyboard="false"
		:closable="false"
		:footer="null"
		wrapClassName="goods-center-batch-buy-dialog-wrap"
		v-model:visible="visible"
		width="560px"
		centered
		destroyOnClose
	>
		<div class="content">
			<div class="banner"></div>
			<div class="container" v-if="notifyDetail">
				<h3>
					当前您有一个批量购买特惠的订单待支付
					<br />
					<span>{{ timeFilter(notifyDetail.activeEndTime) }}</span>
					前购买，可享受特惠折扣，超时后折扣过期！
				</h3>
				<ul class="info-list">
					<li>
						购买线索周期：
						{{ timeFilter(notifyDetail.startTime, 'YYYY-MM-DD') }}
						至
						{{ timeFilter(notifyDetail.endTime, 'YYYY-MM-DD') }}
					</li>
					<li>驾照类型：{{ notifyDetail.driveLicenseType }}</li>
					<li>
						购买线索区域：{{ notifyDetail.area }}
						<span class="warning">（全区线索）</span>
					</li>
					<li>
						最大可购买条数：
						<m-input
							:disabled="loadingPrice"
							v-model:value="limit"
							@change="onChangeLimit"
							:maxlength="4"
						/>
						条
						<span class="warning">（可修改条数）</span>
						<span class="gary" v-if="notifyDetail.minDiscountLimit">
							（最低购买{{ notifyDetail.minDiscountLimit }}条可享受折扣）
						</span>
					</li>
					<li>
						商城购买原价：{{ (notifyDetail.realSaleCoin + notifyDetail.cheapCoin).toFixed(2) }}元宝（{{
							(notifyDetail.realSaleAmount + notifyDetail.cheapAmount).toFixed(2)
						}}元）
					</li>
					<li>
						本次限时特惠预估最多需支付元宝：
						<span class="red">
							<span class="big">{{ notifyDetail.realSaleCoin }}</span>
							元宝（{{ notifyDetail.realSaleAmount }}元）
						</span>
						<br />
						<span class="orange" v-if="notifyDetail.cheapAmount">
							比商城折扣多优惠：{{ notifyDetail.cheapAmount }}元
						</span>
					</li>
				</ul>
			</div>
			<div class="btns">
				<m-button class="btn" type="primary" @click="onBuyClue">查看详情</m-button>
				<m-button
					class="btn"
					v-if="changeLimitTag"
					type="primary"
					@click="getPrice"
					:loading="loadingButton || loadingPrice"
				>
					获取最新价格
				</m-button>
				<m-button
					class="btn"
					v-if="!changeLimitTag"
					type="primary"
					@click="onBuy"
					:loading="loadingButton || loadingPrice"
				>
					立即购买
				</m-button>
			</div>
			<div class="close" @click="onClose"></div>
		</div>
	</pm-dialog>
</template>

<script lang="ts" src="./index.ts"></script>
<style lang="less" src="./index.less" scoped></style>
<style lang="less" src="./index-global.less"></style>
