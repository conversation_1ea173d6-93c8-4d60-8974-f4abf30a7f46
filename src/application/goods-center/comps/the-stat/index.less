.boxs {
	padding: 0 20px;
	display: grid;
	grid-template-columns: repeat(4, 1fr);
	grid-column-gap: 15px;
	grid-row-gap: 15px;

	.box {
		background: rgba(30, 123, 249, 0.05);
		border-radius: 3px;
		padding: 15px 12px;
		font-size: 16px;

		.icon {
			width: 24px;
			height: 24px;
			display: inline-block;
			margin-right: 6px;
			background-position: center;
			background-repeat: no-repeat;
			background-size: cover;

			&.icon-1 {
				background-image: url('./img/icon-1.png');
			}

			&.icon-2 {
				background-image: url('./img/icon-2.png');
			}

			&.icon-3 {
				background-image: url('./img/icon-3.png');
			}

			&.icon-4 {
				background-image: url('./img/icon-4.png');
			}

			&.icon-5 {
				background-image: url('./img/icon-5.png');
			}

			&.icon-6 {
				background-image: url('./img/icon-6.png');
			}

			&.icon-7 {
				background-image: url('./img/icon-7.png');
			}

			&.icon-8 {
				background-image: url('./img/icon-8.png');
			}

			&.icon-9 {
				background-image: url('./img/icon-9.png');
			}
		}

		.box-info {
			display: flex;
			align-items: center;
			font-size: 12px;
			font-weight: 500;
			color: var(--text-secondary);
			line-height: 20px;

			a {
				margin-left: 5px;
				color: var(--ant-primary-color);
			}
		}

		.box-button {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-top: 1px;
			padding-left: 30px;

			strong {
				font-size: 12px;
				line-height: 22px;
				white-space: nowrap;

				span {
					font-size: 16px;
				}
				.color-primary {
					color: var(--ant-primary-color);
				}
				.color-danger {
					color: var(--ant-error-color);
				}
			}

			.mr-center {
				display: flex;
				align-items: center;
				white-space: nowrap;

				.vip-year {
					border: 1px solid #76beff;
					font-size: 12px;
					padding: 1px 5px;
					color: var(--ant-primary-color);
					border-radius: 2px;
					background-color: #e9f6ff;
					margin-left: 6px;
				}
			}

			.ant-btn {
				margin-left: 6px;
				padding: 7px 15px;
				font-size: 12px;
				line-height: 12px;
				height: auto;
			}
		}
	}
}
