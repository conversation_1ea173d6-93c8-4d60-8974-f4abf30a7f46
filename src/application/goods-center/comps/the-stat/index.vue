<template>
	<div class="boxs">
		<!-- 账户余额 -->
		<div class="box">
			<div class="box-info">
				<span class="icon icon-1" />
				<div class="mr-15">
					账户余额
					<a @click="onJumpPayAccountAuth">支付账户授权</a>
				</div>
			</div>
			<div class="box-button">
				<strong>
					<span>{{ filterCount(accountInfoStore.accountAmount) }}</span>
					元
				</strong>
				<m-button type="primary" @click="onRecharge">充值</m-button>
			</div>
		</div>

		<!-- 元宝余额 -->
		<div class="box">
			<div class="box-info">
				<span class="icon icon-2" />
				<div class="mr-15">
					元宝余额
					<a @click="onJumpBalanceDetail">明细</a>
				</div>
			</div>
			<div class="box-button">
				<strong>
					<span>{{ filterCount(accountInfoStore.coinAmount) }}</span>
					个
				</strong>
				<m-button type="primary" :loading="openBuyCoinStore.openLoading" @click="onBuyCoin">购买</m-button>
			</div>
		</div>

		<!-- 会员权益 -->
		<div class="box" v-if="openedTrainTypesStore.data.value.hasCar">
			<div class="box-info">
				<span class="icon icon-3" />
				<div class="mr-15">
					会员权益
					<a @click="onJumpRightsDetail">明细</a>
				</div>
			</div>
			<div class="box-button">
				<div class="mr-center">
					<strong>
						<span v-if="vipInfoStore.expiredDay">已过期 {{ vipInfoStore.expiredDay }} 天</span>
						<span v-else>{{ vipInfoStore.name }}</span>
					</strong>
					<span v-if="vipInfoStore.age > 0" class="vip-year">{{ vipInfoStore.age }}年会员</span>
				</div>
				<m-button v-if="vipInfoStore.btnText" type="primary" @click="onJumpVip">
					{{ vipInfoStore.btnText }}
				</m-button>
			</div>
		</div>

		<!-- 线上支付 驾付宝 -->
		<div v-if="showJiafubao && openedTrainTypesStore.data.value.hasCar" class="box">
			<div class="box-info">
				<span class="icon icon-4" />
				<div class="mr-15">线上支付(可提现)</div>
			</div>
			<div class="box-button">
				<strong>
					<span>{{ filterCount(orderStat?.waitWithdrawAmount) }}</span>
					元
				</strong>
				<m-button type="primary" @click="onJumpJiafubao">创建</m-button>
			</div>
		</div>

		<!-- 短信权益包 -->
		<!-- <div class="box">
			<div class="box-info">
				<span class="icon icon-5" />
				<div class="mr-15">
					短信权益包
					<a @click="onJumpSMSDetail">明细</a>
				</div>
			</div>
			<div class="box-button">
				<strong v-if="!SMSPackageAccount?.haveAccount"><span>助力招生转化</span></strong>
				<strong v-else>
					可用余量：
					<span>{{ filterCount(SMSPackageAccount.totalRemain) }}</span>
					条
				</strong>
				<m-button type="primary" @click="onJumpSMS">购买</m-button>
			</div>
		</div> -->

		<!-- 畅享电话卡 -->
		<div v-if="phoneRightIsShow && openedTrainTypesStore.data.value.hasCar" class="box">
			<div class="box-info">
				<span class="icon icon-9" />
				<div class="mr-15">
					畅享电话卡
					<a @click="onJumpPhoneRightDetail">明细</a>
				</div>
			</div>
			<div class="box-button">
				<strong v-if="!phoneRightView.paid"><span>低价接收高质量电话线索</span></strong>
				<strong v-else-if="phoneRightView.remainUseDays > 0 && phoneRightView.remainUseDays <= 30">
					<span class="color-danger">还有{{ phoneRightView.remainUseDays }}天到期</span>
				</strong>
				<strong v-else-if="phoneRightView.remainUseDays > 30"><span class="color-primary">生效中</span></strong>
				<strong v-else-if="phoneRightView.remainUseDays <= 0"><span class="color-danger">已失效</span></strong>
				<m-button type="primary" @click="onJumpPhoneRight">购买</m-button>
			</div>
		</div>

		<!-- 电话线索包 -->
		<div
			v-if="
				(leadPackageSetting || leadPackageView || leadPackageDetail) && openedTrainTypesStore.data.value.hasCar
			"
			class="box"
		>
			<div class="box-info">
				<span class="icon icon-6" />
				<div>
					电话线索包
					<a @click="onJumpPhoneDetail">明细</a>
				</div>
			</div>
			<div class="box-button">
				<strong v-if="!leadPackageView"><span>买电话线索送微信</span></strong>
				<strong v-else-if="leadPackageView.waitUseDays">
					待使用：
					<span>{{ filterCount(leadPackageView.waitUseDays) }}</span>
					天
				</strong>
				<strong v-else-if="leadPackageView.expiresDays">
					已过期：
					<span>{{ filterCount(leadPackageView.expiresDays) }}</span>
					天
				</strong>
				<m-button type="primary" @click="onJumpPhone" v-if="leadPackageSetting">购买</m-button>
			</div>
		</div>

		<!-- 线索商城 -->
		<div class="box" v-if="openedTrainTypesStore.data.value.hasCar">
			<div class="box-info">
				<span class="icon icon-7" />
				<div class="mr-15">
					线索商城
					<a @click="onJumpOrderDetail">明细</a>
				</div>
			</div>
			<div class="box-button">
				<div class="mr-center">
					<strong>
						<span>{{ clueMallInfo.main }}</span>
					</strong>
				</div>
				<m-button type="primary" @click="onJumpBuyClue">
					{{ clueMallInfo.btnText }}
				</m-button>
			</div>
		</div>

		<!-- 分销兑换元宝 -->
		<div class="box" v-if="saleExchangeIsOpen && openedTrainTypesStore.data.value.hasCar">
			<div class="box-info">
				<span class="icon icon-8" />
				<div class="mr-15">
					用户列表
					<a @click="onJumpDFGDetail">明细</a>
				</div>
			</div>
			<div class="box-button">
				<strong><span>分销兑换元宝</span></strong>
				<m-button type="primary" @click="onJumpDFG">去邀请</m-button>
			</div>
		</div>
	</div>
</template>

<script lang="ts" src="./index.ts"></script>
<style lang="less" src="./index.less" scoped></style>
