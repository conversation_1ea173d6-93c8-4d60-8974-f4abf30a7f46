import { RechargeCallbackDataModel } from '@/components/recharge-dialog/types';
import { useAccountInfoStore } from '@/pinia/account-info';
import { useCertificationInfoStore } from '@/pinia/certification-info';
import { useOpenBuyCoinStore } from '@/pinia/open-buy-coin';
import { BuyCoinCallbackDataModel } from '@/pinia/open-buy-coin/types';
import { useUserProfileStore } from '@/pinia/user-profile';
import { useVipInfoStore } from '@/pinia/vip-info';
import { TradeGoodsEnum, JiaxiaoTypeEnum, SubTypeEnum } from '@/utils/constant';
import { MESSAGE_TYPE, MUtils, PaasPostMessage } from '@paas/paas-library';
import { computed, defineComponent, onMounted, reactive, toRefs } from 'vue';
import {
	GetHasOrderListStore,
	GetLeadPackageDetailStore,
	GetLeadPackageStore,
	GetLeadPackageViewStore,
	GetOrderStatStore,
	GetSaleExchangeIsOpenStore,
	GetShowPrivilegeStore,
	GetSMSPackageAccountStore,
	GetShowInfoStore
} from './store';
import { StateModel } from './types';
import { CheckTypeEnum } from '@/pinia/certification-info/constant';
import { PopupUidEnum } from '@/utils/constants/common';
import { useOpenedTrainTypesStore } from '@/utils/query';
// import { useRechargeStore } from '@/pinia/recharge';

export default defineComponent({
	setup() {
		const certificationInfoStore = useCertificationInfoStore();
		const accountInfoStore = useAccountInfoStore();
		const openBuyCoinStore = useOpenBuyCoinStore();
		const userProfileStore = useUserProfileStore();
		const vipInfoStore = useVipInfoStore();
		const openedTrainTypesStore = useOpenedTrainTypesStore();

		// 内部属性
		const state = reactive<StateModel>({
			// 是否显示驾付宝部分
			showJiafubao: false,
			// 驾付宝订单统计数据
			orderStat: null,
			// 短信包账户信息
			SMSPackageAccount: null,
			// 电话线索包配置
			leadPackageSetting: null,
			// 电话线索包展示
			leadPackageView: null,
			// 电话线索包明细
			leadPackageDetail: null,
			// 畅享电话卡展示
			phoneRightView: null,
			// 畅享电话卡展示是否显示
			phoneRightIsShow: false,
			// 待支付订单条数
			orderNumber: 0,
			// 是否开启分销
			saleExchangeIsOpen: false
		});

		const computeds = {
			// 线索商城信息
			clueMallInfo: computed(() => {
				const main = state.orderNumber > 0 ? `${state.orderNumber}笔待支付订单` : '暂无待支付订单';
				const btnText = state.orderNumber > 0 ? '去支付' : '去购买';

				return {
					main,
					btnText
				};
			})
		};

		// 方法
		const methods = {
			async init() {
				accountInfoStore.fetchAccountAndCoinInfo();

				if (!openedTrainTypesStore.isFetched.value || !openedTrainTypesStore.isSuccess.value) {
					await openedTrainTypesStore.suspense();
				}
				if (openedTrainTypesStore.data.value.hasCar) {
					vipInfoStore.fetchVipInfo();
					// 驾付宝
					methods.getShowPrivilege();
					// 短信包
					methods.getSMSPackageAccount();
					// // 电话线索包配置
					// methods.getLeadPackage();
					// // 电话线索包展示
					// methods.getLeadPackageView();
					// // 电话线索包明细
					// methods.getLeadPackageDetail();
					// 畅享电话卡展示
					methods.getPhoneRightView();
					// 线索商城批量购买待支付订单数
					methods.getHasOrderList();
					// 是否开启分销
					methods.getSaleExchangeIsOpen();
				}
			},
			// 是否展示驾付宝
			getShowPrivilege() {
				GetShowPrivilegeStore.request()
					.getData()
					.then(({ value }) => {
						state.showJiafubao = value;

						if (value) {
							methods.getOrderStat();
						}
					});
			},
			// 获取驾付宝订单统计数据
			getOrderStat() {
				GetOrderStatStore.request()
					.getData()
					.then(data => {
						state.orderStat = data;
					})
					.catch(e => {
						console.log(e);
					});
			},
			// 短信包
			getSMSPackageAccount() {
				GetSMSPackageAccountStore.request()
					.getData()
					.then(data => {
						state.SMSPackageAccount = data;
					})
					.catch(() => {
						// 未购买短信包
						state.SMSPackageAccount = null;
					});
			},
			// 电话线索包配置
			getLeadPackage() {
				GetLeadPackageStore.request({ tradeGoods: TradeGoodsEnum.PHONE_LEAD_PACKAGE })
					.getData()
					.then(data => {
						state.leadPackageSetting = data?.length > 0 ? data : null;
					});
			},
			// 电话线索包展示
			getLeadPackageView() {
				GetLeadPackageViewStore.request({ tradeGoods: TradeGoodsEnum.PHONE_LEAD_PACKAGE })
					.getData()
					.then(data => {
						state.leadPackageView = data;
					});
			},
			// 电话线索包明细
			getLeadPackageDetail() {
				GetLeadPackageDetailStore.request({ tradeGoods: TradeGoodsEnum.PHONE_LEAD_PACKAGE })
					.getData()
					.then(data => {
						state.leadPackageDetail = data?.length > 0 ? data : null;
					});
			},
			// 畅享电话卡展示
			getPhoneRightView() {
				GetShowInfoStore.request()
					.getData()
					.then(res => {
						state.phoneRightView = res;
						if (res.paid) {
							state.phoneRightIsShow = true;
						} else {
							if (
								(res.jxType === JiaxiaoTypeEnum.MASTER || res.jxSubType === SubTypeEnum.INDEPENDENT) &&
								res.inCity &&
								res.jxCertification &&
								!res.vip &&
								!res.recentAutoReceiveLead
							) {
								state.phoneRightIsShow = true;
							}
						}
					});
			},
			// 线索商城批量购买待支付订单数
			getHasOrderList() {
				GetHasOrderListStore.request()
					.getData()
					.then(data => {
						state.orderNumber = data.value;
					});
			},
			// 是否开启分销
			getSaleExchangeIsOpen() {
				GetSaleExchangeIsOpenStore.request()
					.getData()
					.then(data => {
						state.saleExchangeIsOpen = !!data?.value;
					});
			},
			// 充值
			async onRecharge() {
				// emitter.emit(EmitterEnum.RECHARGE);
				if (userProfileStore.isSuperAdmin) {
					await PaasPostMessage.post('main://navigation.to', 'H3223', {
						appName: 'buick',
						uid: PopupUidEnum.ACCOUNT_BATCH_AUTH
					});
				}

				const isAth = await certificationInfoStore.checkAuth(CheckTypeEnum.CAR_AUTH_OR_UAV);
				if (!isAth) {
					return;
				}

				PaasPostMessage.post('main://navigation.to', 'H3222', {
					appName: 'buick',
					uid: PopupUidEnum.RECHARGE
					// query: {
					// 	amount: 0.01
					// }
				}).then((data: RechargeCallbackDataModel) => {
					console.log('充值弹窗关闭: ', data);
					accountInfoStore.fetchAccountAndCoinInfo();
				});
			},
			// 购买元宝
			async onBuyCoin() {
				// openBuyCoinStore.open();
				if (userProfileStore.isSuperAdmin) {
					await PaasPostMessage.post('main://navigation.to', 'H3223', {
						appName: 'buick',
						uid: PopupUidEnum.ACCOUNT_BATCH_AUTH
					});
				}

				const isAth = await certificationInfoStore.checkAuth(CheckTypeEnum.CAR_OR_UAV);
				if (!isAth) {
					return;
				}

				PaasPostMessage.post('main://navigation.to', 'H3221', {
					appName: 'buick',
					uid: PopupUidEnum.BUY_COIN
				}).then((data: BuyCoinCallbackDataModel) => {
					console.log('购买元宝弹窗关闭: ', data);
					setTimeout(() => {
						accountInfoStore.fetchAccountAndCoinInfo();
					}, 500);
				});
			},
			// 元宝余额
			onJumpBalanceDetail() {
				PaasPostMessage.post('base://get.path-jump', {
					id: 'H3201',
					appName: 'buick'
				});
			},
			// 会员权益
			onJumpRightsDetail() {
				PaasPostMessage.post('base://get.path-jump', {
					id: 'H3202',
					appName: 'buick'
				});
			},
			// 购买vip
			async onJumpVip() {
				const btnText = vipInfoStore.btnText;
				if (['购买', '续费', '升级'].includes(btnText)) {
					MUtils.sendTongji([`会员${btnText}`, '商城中心', '商城中心', `会员${btnText}`]);
				}

				const isAth = await certificationInfoStore.checkAuth(CheckTypeEnum.CAR);
				if (!isAth) {
					return;
				}

				PaasPostMessage.post('base://get.path-jump', {
					id: 'W190101',
					appName: 'jiaxiao-vip'
				});
			},
			// 驾付宝创建
			async onJumpJiafubao() {
				MUtils.sendTongji(['线上支付创建', '商城中心', '商城中心', '线上支付创建']);

				const isAth = await certificationInfoStore.checkAuth(CheckTypeEnum.CAR);
				if (!isAth) {
					return;
				}

				PaasPostMessage.post('base://get.path-jump', {
					id: 'W340102',
					appName: 'jaguar'
				});
			},
			// 短信包详情
			onJumpSMSDetail() {
				if (!state.SMSPackageAccount) {
					MUtils.toast('您还未购买当前商品，暂无明细信息可查看', MESSAGE_TYPE.error);
					return;
				}

				PaasPostMessage.post('base://get.path-jump', {
					id: 'H3205',
					appName: 'buick'
				});
			},
			// 购买短信包
			async onJumpSMS() {
				MUtils.sendTongji(['短信权益包购买', '商城中心', '商城中心', '短信权益包购买']);

				const isAth = await certificationInfoStore.checkAuth(CheckTypeEnum.CAR);
				if (!isAth) {
					return;
				}

				if (!userProfileStore.isSuperAdmin) {
					MUtils.toast('员工账号无权限购买，请联系管理员', MESSAGE_TYPE.error);
					return;
				}

				PaasPostMessage.post('base://get.path-jump', {
					id: 'H3204',
					appName: 'buick'
				});
			},
			// 畅享电话卡
			async onJumpPhoneRight() {
				if (!userProfileStore.isSuperAdmin) {
					MUtils.toast('员工账号无权限购买，请联系管理员', MESSAGE_TYPE.error);
					return;
				}
				PaasPostMessage.post('base://get.path-jump', {
					id: 'H3224',
					appName: 'buick'
				});
			},
			// 畅享电话卡详情
			onJumpPhoneRightDetail() {
				if (!state.phoneRightView.paid) {
					MUtils.toast('您还未购买当前商品，暂无明细信息可查看', MESSAGE_TYPE.error);
					return;
				}
				PaasPostMessage.post('base://get.path-jump', {
					id: 'H3225',
					appName: 'buick'
				});
			},
			// 电话线索包详情
			onJumpPhoneDetail() {
				PaasPostMessage.post('base://get.path-jump', {
					id: 'H3207',
					appName: 'buick'
				});
			},
			// 购买电话线索包
			async onJumpPhone() {
				MUtils.sendTongji(['电话线索包购买', '商城中心', '商城中心', '电话线索包购买']);

				const isAth = await certificationInfoStore.checkAuth(CheckTypeEnum.CAR);
				if (!isAth) {
					return;
				}

				if (!userProfileStore.isSuperAdmin) {
					MUtils.toast('员工账号无权限购买，请联系管理员', MESSAGE_TYPE.error);
					return;
				}

				PaasPostMessage.post('base://get.path-jump', {
					id: 'H3206',
					appName: 'buick'
				});
			},
			// 线索商城明细
			onJumpOrderDetail() {
				if (!userProfileStore.isSuperAdmin && !userProfileStore.isRecruitDirector) {
					MUtils.toast('抱歉，你无当前操作权限', MESSAGE_TYPE.error);
					return;
				}

				PaasPostMessage.post('base://get.path-jump', {
					id: 'H3208',
					appName: 'buick'
				});
			},
			// 线索商城购买
			async onJumpBuyClue() {
				const tempText = computeds.clueMallInfo.value.btnText;
				MUtils.sendTongji([`线索商城${tempText}`, '商城中心', '商城中心', `线索商城${tempText}`]);

				const isAth = await certificationInfoStore.checkAuth(CheckTypeEnum.CAR);
				if (!isAth) {
					return;
				}

				if (!userProfileStore.isSuperAdmin && !userProfileStore.isRecruitDirector) {
					MUtils.toast('员工账号无权限购买，请联系管理员', MESSAGE_TYPE.error);
					return;
				}

				if (state.orderNumber > 0) {
					PaasPostMessage.post('base://get.path-jump', {
						id: 'H3208',
						appName: 'buick'
					});
				} else {
					PaasPostMessage.post('base://get.path-jump', {
						id: 'W14010105',
						appName: 'lincoln',
						query: {
							tab: 'clueStore',
							bathBuy: true
						}
					});
				}
			},
			// 分销详情
			onJumpDFGDetail() {
				PaasPostMessage.post('base://get.path-jump', {
					id: 'H3210',
					appName: 'buick'
				});
			},
			// 分销邀请
			async onJumpDFG() {
				MUtils.sendTongji(['分销换元宝', '商城中心', '商城中心', '分销换元宝']);

				const isAth = await certificationInfoStore.checkAuth(CheckTypeEnum.CAR);
				if (!isAth) {
					return;
				}

				if (!userProfileStore.isSuperAdmin) {
					MUtils.toast('员工账号无权限邀请，请联系管理员', MESSAGE_TYPE.error);
					return;
				}

				PaasPostMessage.post('base://get.path-jump', {
					id: 'H3209',
					appName: 'buick'
				});
			},
			// 支付账户授权页
			onJumpPayAccountAuth() {
				if (!userProfileStore.isSuperAdmin) {
					MUtils.toast('员工无操作权限，请联系超级管理员', MESSAGE_TYPE.error);
					return;
				}

				PaasPostMessage.post('base://get.path-jump', {
					id: 'H3211',
					appName: 'buick'
				});
			},
			filterCount(val) {
				if (typeof val === 'number' || typeof val === 'string') {
					return val;
				}
				return '-';
			}
		};

		onMounted(() => {
			methods.init();
		});

		return {
			openedTrainTypesStore,
			accountInfoStore,
			openBuyCoinStore,
			vipInfoStore,
			...toRefs(state),
			...computeds,
			...methods
		};
	}
});
