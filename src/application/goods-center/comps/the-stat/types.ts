import { JiaxiaoTypeEnum, SubTypeEnum } from '@/utils/constant';

export interface StateModel {
	// 是否显示驾付宝部分
	showJiafubao: boolean;
	// 订单统计数据
	orderStat: OrderStatResponse;
	// 短信包账户信息
	SMSPackageAccount: SMSPackageAccountResponse;
	// 电话线索包配置
	leadPackageSetting: LeadPackageResponse[];
	// 电话线索包展示
	leadPackageView: LeadPackageViewResponse;
	// 电话线索包明细
	leadPackageDetail: LeadPackageDetailResponse[];
	// 畅享电话卡展示
	phoneRightView: ShowInfoResponse;
	// 畅享电话卡展示是否显示
	phoneRightIsShow: boolean;
	//待支付订单条数
	orderNumber: number;
	// 是否开启分销
	saleExchangeIsOpen: boolean;
}

// 订单统计数据
export interface OrderStatResponse {
	waitWithdrawAmount: number;
}

// 短信权益包账户信息
export interface SMSPackageAccountResponse {
	/** 商家类型 */
	targetType: number;
	/** 商家ID */
	targetId: number;
	/** 剩余的总短信数量 */
	totalRemain: number;
	/** 剩余赠送的短信数量 */
	freeRemain: number;
	/** 剩余支付的短信数量 */
	payRemain: number;
	/** 已使用短信总量 */
	costTotal: number;
	/** 已使用赠送短信总量 */
	costFree: number;
	/** 已使用支付短信总量 */
	costPay: number;
	/** 短信总量 */
	allTotal: number;
	/** 支付短信总量 */
	payTotal: number;
	/** 赠送的短信总量 */
	freeTotal: number;
	/** 支付的总金额，单位分 */
	paidTotalCent: number;
	/** 创建时间 */
	createTime: number;
	/** 修改时间 */
	updateTime: number;
	/** 商家名称 */
	targetName: string;
	/** 城市code */
	cityCode: string;
	/** 城市名称 */
	cityName: string;
	/** 是否有短信包账户 */
	haveAccount: boolean;
}

// 电话线索包配置
export interface LeadPackageResponse {
	/** 商品code */
	goodsCode: string;
	/** 线索包商品类型,可用值:UNKNOWN,SILVER_VIP,GOLD_VIP,DIAMOND_VIP,BLACK_GOLD_VIP,SEARCH_AD,ENTER_ADVERT,RANK_ADVERT,BIG_CAR_LEAD_PACKAGE,MOTOR_LEAD_PACKAGE,PHONE_LEAD_PACKAGE,OFFLINE_SERVICE,COIN,SMS_PACK,PAAS */
	tradeGoodsEnum: string;
	/** 有效天数 */
	effectiveDay: number;
	/** 价格(元) */
	price: number;
	/** 商品名称 */
	goodsName: string;
}

// 电话线索包展示
export interface LeadPackageViewResponse {
	/** 待使用天数 */
	waitUseDays: number;
	/** 已过期天数 */
	expiresDays: number;
}

// 电话线索包明细
export interface LeadPackageDetailResponse {
	/**  */
	paidTime: number;
	/** 支付金额 */
	goodsPaid: number;
	/** 商品名称 */
	goodsName: string;
	/** 订单有效期开始时间 */
	expirationStart: number;
	/** 订单有效期结束时间：退款后该时间会更新 */
	expirationEnd: number;
	/** 订单生效状态,直接展示 */
	statusStr: string;
	/** 购买人 */
	createUser: string;
}

export interface ShowInfoResponse {
	/** 是否会员 */
	vip: boolean;
	/** 是否驾校认证 */
	jxCertification: boolean;
	/** 是否招生授权 */
	recruitCertification: boolean;
	/** 是否驾校真实性免审 */
	realExemptionExpire: boolean;
	/** 是否驾校资质免审 */
	qualificationExemptionExpire: boolean;
	/** 近30天是否自动接收线索 */
	recentAutoReceiveLead: boolean;
	/** 是否购买过 */
	paid: boolean;
	/** 畅享电话卡剩余使用天数 */
	remainUseDays: number;
	/** 最新一笔购买生效中的订单id */
	latestPayOrderId: number;
	/** 最新一笔购买生效中订单支付流水号 */
	latestPayNumber: string;
	/** 是否在配置城市 */
	inCity: boolean;
	/** 是否后台代下单 */
	adminPlaceOrder: boolean;
	/** 驾校类型 */
	jxType: JiaxiaoTypeEnum;
	/** 驾校子类型 */
	jxSubType: SubTypeEnum;
}
