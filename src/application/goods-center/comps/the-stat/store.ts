import { OrderStatStore, ShowPrivilegeStore } from '@/store/buick/h5';
import {
	LeadPackageDetailStore,
	LeadPackageStore,
	LeadPackageViewStore,
	SaleExchangeIsOpenStore,
	ShowInfoStore,
	SMSPackageAccountStore
} from '@/store/jiaxiao-vip/web';
import { HasOrderListStore } from '@/store/volvo/h5';
import {
	LeadPackageDetailResponse,
	LeadPackageResponse,
	LeadPackageViewResponse,
	OrderStatResponse,
	ShowInfoResponse,
	SMSPackageAccountResponse
} from './types';

export const GetShowPrivilegeStore = new ShowPrivilegeStore<{ value: boolean }>({});

export const GetOrderStatStore = new OrderStatStore<OrderStatResponse>({});

export const GetSMSPackageAccountStore = new SMSPackageAccountStore<SMSPackageAccountResponse>({});

export const GetLeadPackageStore = new LeadPackageStore<LeadPackageResponse[]>({});

export const GetLeadPackageViewStore = new LeadPackageViewStore<LeadPackageViewResponse>({});

export const GetLeadPackageDetailStore = new LeadPackageDetailStore<LeadPackageDetailResponse[]>({});

export const GetHasOrderListStore = new HasOrderListStore<{ value: number }>({});

export const GetSaleExchangeIsOpenStore = new SaleExchangeIsOpenStore<{ value: boolean }>({});

export const GetShowInfoStore = new ShowInfoStore<ShowInfoResponse>({});
