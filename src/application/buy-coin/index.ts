import BuyCoinDialogComp from '@/components/buy-coin-dialog/index.vue';
import { useOpenBuyCoinStore } from '@/pinia/open-buy-coin';
import { BuyCoinCallbackDataModel } from '@/pinia/open-buy-coin/types';
import { PaasPostMessage } from '@paas/paas-library';
import { defineComponent, onMounted } from 'vue';

export default defineComponent({
	components: {
		BuyCoinDialogComp
	},
	setup() {
		const openBuyCoinStore = useOpenBuyCoinStore();

		const methods = {
			open() {
				openBuyCoinStore.open({
					beforeCallback: ({ canBuy }) => {
						if (!canBuy) {
							PaasPostMessage.post('popup.close');
						}
					},
					callback: (data: BuyCoinCallbackDataModel) => {
						console.log(24, data);
						if (data.isClose) {
							PaasPostMessage.post('main://popup.back', 1, data);
						}
					}
				});
			}
		};

		onMounted(() => {
			methods.open();
		});

		return {
			openBuyCoinStore,
			...methods
		};
	}
});
