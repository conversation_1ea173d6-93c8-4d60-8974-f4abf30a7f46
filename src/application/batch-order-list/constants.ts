import { ColorEnum } from '@/components/text-color/constants';

export enum StatusEnum {
	ING,
	SUCCESS,
	FAIL,
	CANCEL,
	WAIT,
	EXPIRED
}

// 来源
export enum SourceEnum {
	// 后台手动购买
	VOLVO_ADMIN = 1,
	// vip服务开通下单
	VIP_ADMIN,
	// 驾校手动购买
	PAAS,
	// 设置自动
	AUTO_TASK,
	// 定时自动购买
	AUTO_BUY_FIXED_TIME,
	// 立即自动购买
	AUTO_BUY_AT_ONCE,
	// 批量折扣推送购买
	NOTIFY_BATCH_ORDER,
	// 批量折扣推送购买自动
	NOTIFY_BATCH_ORDER_AUTO
}

export const STATUS_LIST = [
	{
		value: StatusEnum.ING,
		name: '支付中',
		color: ColorEnum.ORANGE
	},
	{
		value: StatusEnum.SUCCESS,
		name: '支付成功',
		color: ColorEnum.GREEN
	},
	{
		value: StatusEnum.FAIL,
		name: '支付失败',
		color: ColorEnum.RED
	},
	{
		value: StatusEnum.CANCEL,
		name: '支付取消',
		color: ColorEnum.PINK
	},
	{
		value: StatusEnum.WAIT,
		name: '待支付',
		color: ColorEnum.CYAN
	},
	{
		value: StatusEnum.EXPIRED,
		name: '已过期',
		color: ColorEnum.RED
	}
];
