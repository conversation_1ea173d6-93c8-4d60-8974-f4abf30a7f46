import { BatchOrderItemResponse } from '@/application/batch-order-list/types';
import {
	BatchOrderCancelStore,
	BatchOrderCopyStore,
	BatchOrderListStore,
	BatchOrderPayByOrderStore
} from '@/store/volvo/h5';

export const GetBatchOrderListStore = new BatchOrderListStore<BatchOrderItemResponse[]>({});

export const FetchBatchOrderPayByOrderStore = new BatchOrderPayByOrderStore<{ value: boolean }>({});

export const FetchBatchOrderCopyStore = new BatchOrderCopyStore<{ value: boolean }>({});

export const FetchBatchOrderCancelStore = new BatchOrderCancelStore<{ value: boolean }>({});
