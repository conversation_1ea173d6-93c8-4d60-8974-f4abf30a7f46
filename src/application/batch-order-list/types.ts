import { SourceEnum, StatusEnum } from '@/application/batch-order-list/constants';

export interface StateModel {
	showList: boolean;
	loading: boolean;
}

export interface BatchOrderItemResponse {
	/** 订单id */
	id: number;
	/** 商家类型，2-教练3-驾校 */
	targetType: number;
	/** 商家名称 */
	targetName: string;
	/** 商家id */
	targetId: number;
	/** 订单状态，0-支付中 1-支付成功2-支付失败 3-支付取消 4-待支付 5-已过期 */
	status: StatusEnum;
	/** 预计购买数量 */
	totalCount: number;
	/** 预计支付数额 */
	prePaidVal: number;
	/** 售卖方式 0固定价格 1后台余量线索折扣 2批量购买余量折扣 */
	soldType: number;
	/** 售卖方式对应描述 */
	soldTypeDesc: string;
	/** 售卖方式对应的值 */
	soldTypeVal: string;
	/** 实际购买数量 */
	soldCount: number;
	/** 支付货币类型，1-金币2-现金3-元宝 */
	paidType: number;
	/** 实际支付数额 */
	paidVal: number;
	/** 来源 1后台 3驾校 */
	source: SourceEnum;
	/** 创建人 */
	createUser: string;
	/** 创建人姓名 */
	createUserName: string;
	/** 购买时选择的查询条件JSON:{"startTime":1660126074040,"endTime":1660126095045,"driveLicenseType":"C1,C2,C3","userAreaCode":"420103,420105","distance":15000} */
	queryCondition: string;
	/** 查询区域列表 */
	queryAreaList: string;
	/** 咨询时间范围 */
	queryTimeRange: string;
	/** 创建时间 */
	createTime: number;
	/** 驾照类型，多个,分隔 */
	driveLicenseType: string;
	/** 来源 1后台 3驾校 */
	sourceDesc: string;
	/** 是否自动购买 */
	autoBuy: boolean;
	/** 是否自动购买任务类型 */
	autoBuyType: number;
	/** 是否自动购买任务类型 */
	autoBuyTypeDesc: string;
	/** 自动购买起始时间 */
	autoBuyBeginTime: number;
	/** 自动购买截止时间 */
	autoBuyEndTime: number;
	/** 自动购买限制条数,-1表示全量 */
	autoBuyLimit: number;
	/** 余额不足购买失败数量 */
	balanceNotEnoughCount: number;
	/** 平台圈列表 */
	cityClueAreaList: string;
	/** 修改人 */
	updateUser: string;
	/** 修改人姓名 */
	updateUserName: string;
	/** 线索类型描述 */
	mallLeadType: number;
	/** 线索类型描述 */
	mallLeadTypeDesc: string;
}
