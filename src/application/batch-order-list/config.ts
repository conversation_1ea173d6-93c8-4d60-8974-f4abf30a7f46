import { ColumnXtype, TableColumn, TableDateFormat } from '@paas/paas-library';

export function getTableColumn(): TableColumn[] {
	return [
		{
			title: '订单创建时间',
			dataIndex: 'createTime',
			dateFormat: TableDateFormat.MINUTES,
			width: 200
		},
		{
			title: '选择购买线索时间',
			dataIndex: 'queryTimeRange',
			width: 200
		},
		{
			title: '驾照类型',
			dataIndex: 'driveLicenseType'
		},
		{
			title: '区域',
			dataIndex: 'queryAreaList'
		},
		{
			title: '预估购买条数',
			dataIndex: 'totalCount',
			width: 120
		},
		{
			title: '预估消耗元宝',
			dataIndex: 'prePaidVal',
			width: 120
		},
		{
			title: '实际购买条数',
			dataIndex: 'soldCount',
			width: 120
		},
		{
			title: '实际消耗元宝',
			dataIndex: 'paidVal',
			width: 120
		},
		{
			title: '状态',
			dataIndex: 'status',
			xtype: ColumnXtype.CUSTOM,
			useConfigFixed: false,
			width: 100
		}
	];
}
