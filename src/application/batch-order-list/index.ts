import { getTableColumn } from '@/application/batch-order-list/config';
import { SourceEnum, STATUS_LIST, StatusEnum } from '@/application/batch-order-list/constants';
import {
	FetchBatchOrderCancelStore,
	FetchBatchOrderCopyStore,
	FetchBatchOrderPayByOrderStore,
	GetBatchOrderListStore
} from '@/application/batch-order-list/store';
import TextColorComp from '@/components/text-color/index.vue';
import { useAccountInfoStore } from '@/pinia/account-info';
import { useUserProfileStore } from '@/pinia/user-profile';
import { MESSAGE_TYPE, ModelController, MUtils, PaasPostMessage } from '@paas/paas-library';
import { defineComponent, reactive, toRefs } from 'vue';
import { BatchOrderItemResponse, StateModel } from './types';
import { PopupUidEnum } from '@/utils/constants/common';

export default defineComponent({
	components: { TextColorComp },
	setup() {
		const accountInfoStore = useAccountInfoStore();
		const userProfileStore = useUserProfileStore();

		const state = reactive<StateModel>({
			showList: false,
			loading: false
		});

		const controller = new ModelController({
			table: {
				store: GetBatchOrderListStore
			}
		});

		controller.table.onRequest.use(params => {
			state.loading = true;
			return params;
		});
		controller.table.onResponse.use(responseData => {
			state.showList = !!responseData.data?.length;
			state.loading = false;
			return responseData;
		});

		// 常量
		const constants = {
			StatusEnum,
			SourceEnum,
			COLUMNS: getTableColumn()
		};

		// 方法
		const methods = {
			getTypeRender(lineData: BatchOrderItemResponse) {
				const item = STATUS_LIST.find(v => v.value === lineData.status);

				return {
					color: item.color,
					dsc: item.name
				};
			},
			// 购买
			async onBuy(lineData) {
				await accountInfoStore.fetchAccountAndCoinInfo();
				if (!accountInfoStore.isSuccess) {
					return;
				}

				if (Number(accountInfoStore.coinAmount) < lineData.prePaidVal) {
					const style = {
						content: `
							position: relative;
							text-align: left;
							color: #333333;
						`,
						h2: `
							margin-top: 0;
							margin-bottom: 12px;
							font-size: 16px;
						`,
						body: `
							display: flex;
							margin-bottom: 12px;
							color: #666666;
						`,
						item: `
							width: 300px;
						`,
						span: `
							color: var(--color-danger);
							font-size: 14px;
						`
					};

					const content = `<div style="${style.content}">
						<h2 style="${style.h2}">您的元宝余额不足，请购买元宝后，再来购买</h2>
						<div style="${style.body}">
							<div style="${style.item}">您的元宝余额：<span style="${style.span}">${accountInfoStore.coinAmount}</span> 元宝</div>
							<div>本次购买所需元宝：<span style="${style.span}">${lineData.prePaidVal}</span> 元宝</div>
						</div>
					</div>`;

					MUtils.confirm({
						title: '批量购买提示',
						content,
						type: MESSAGE_TYPE.warning,
						confirmText: '购买元宝',
						style: 'width: 600px;'
					}).then(bool => {
						if (!bool) {
							return;
						}
						PaasPostMessage.post('main://navigation.to', 'H3221', {
							appName: 'buick',
							uid: PopupUidEnum.BUY_COIN
						}).then(() => {
							controller.tableRequest();
						});
					});
					return;
				}

				FetchBatchOrderPayByOrderStore.request({ id: lineData.id })
					.getData()
					.then(data => {
						if (data.value) {
							MUtils.toast('购买提交成功，实际购买数据，会在站内信通知您！', MESSAGE_TYPE.success);
							controller.tableRequest();
						}
					});
			},
			// 复制
			onCopy(lineData) {
				MUtils.confirm({
					title: '提示',
					content: '是否复制该订单',
					type: MESSAGE_TYPE.warning
				}).then(bool => {
					if (!bool) {
						return;
					}
					FetchBatchOrderCopyStore.request({ id: lineData.id })
						.getData()
						.then(data => {
							if (data.value) {
								MUtils.toast('复制订单成功', MESSAGE_TYPE.success);
								controller.tableRequest();
							}
						});
				});
			},
			// 编辑
			onEdit(lineData) {
				const queryCondition = {
					...JSON.parse(lineData.queryCondition),
					batchOrderId: lineData.id
				};
				PaasPostMessage.post('base://get.path-jump', {
					id: 'W14010105',
					appName: 'lincoln',
					query: {
						tab: 'clueStore',
						bathBuy: true,
						queryCondition: encodeURIComponent(JSON.stringify(queryCondition))
					}
				});
			},
			// 取消
			onCancel(lineData) {
				const style = {
					content: `
						position: relative;
						text-align: left;
						color: #333333;
					`,
					h2: `
						margin-top: 0;
						margin-bottom: 12px;
						font-size: 16px;
					`,
					body: `
						display: flex;
						margin-bottom: 12px;
						color: #666666;
					`
				};

				const content = `<div style="${style.content}">
					<h2 style="${style.h2}">确认是否取消该订单</h2>
					<div style="${style.body}">
						<div>订单取消后，不可恢复</div>
					</div>
				</div>`;

				MUtils.confirm({
					title: '取消提示',
					content,
					type: MESSAGE_TYPE.warning
				}).then(bool => {
					if (!bool) {
						return;
					}
					FetchBatchOrderCancelStore.request({ id: lineData.id })
						.getData()
						.then(data => {
							if (data) {
								MUtils.toast('取消订单成功', MESSAGE_TYPE.success);
								controller.tableRequest();
							}
						});
				});
			},
			onBuyClue() {
				if (!userProfileStore.isSuperAdmin && !userProfileStore.isRecruitDirector) {
					MUtils.toast('员工账号无权限购买，请联系管理员', MESSAGE_TYPE.error);
					return;
				}

				PaasPostMessage.post('base://get.path-jump', {
					id: 'W14010105',
					appName: 'lincoln',
					query: {
						tab: 'clueStore',
						bathBuy: true
					}
				});
			}
		};

		return {
			controller,
			...toRefs(state),
			...constants,
			...methods
		};
	}
});
