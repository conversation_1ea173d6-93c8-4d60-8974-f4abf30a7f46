<template>
	<div class="app-container batch-order-list">
		<div class="title">订单列表</div>

		<pm-effi :controller="controller" v-show="showList">
			<pm-search></pm-search>

			<pm-table
				:columns="COLUMNS"
				:scroll="{ x: 1500 }"
				:useCustomColumn="true"
				:showCustom="true"
				:helpIcon="false"
				:operations-width="270"
				:operations-fixed="true"
			>
				<template #status="{ record }">
					<text-color-comp :color="getTypeRender(record).color">
						{{ getTypeRender(record).dsc }}
					</text-color-comp>
				</template>

				<template #operations="{ record }">
					<div class="operation-content">
						<m-button type="link" v-if="record.status === StatusEnum.WAIT" @click="onBuy(record)">
							购买
						</m-button>
						<m-button type="link" v-if="record.source === SourceEnum.PAAS" @click="onCopy(record)">
							复制
						</m-button>
						<m-button
							type="link"
							v-if="record.source === SourceEnum.PAAS && record.status === StatusEnum.WAIT"
							@click="onEdit(record)"
						>
							编辑
						</m-button>
						<m-button
							type="link"
							v-if="record.source === SourceEnum.PAAS && record.status === StatusEnum.WAIT"
							@click="onCancel(record)"
						>
							取消
						</m-button>
					</div>
				</template>
			</pm-table>
		</pm-effi>

		<div class="empty-box" v-show="!showList">
			<m-spin :spinning="loading">
				<div class="feed-back"></div>
				<p>暂无支付订单</p>
				<m-button type="primary" @click="onBuyClue">去购买</m-button>
			</m-spin>
		</div>
	</div>
</template>

<script lang="ts" src="./index.ts"></script>
<style lang="less" src="./index.less" scoped></style>
