<template>
	<div class="full-loading" v-if="loading">
		<m-spin :spinning="loading" />
	</div>
	<div v-show="!loading">
		<update-notice-dialog-comp ref="updateNoticeDialogRef" @agree="onAgree" />
		<pay-account-auth-batch-dialog-comp ref="payAccountAuthBatchDialogRef" @close="onClose" />
	</div>
</template>

<script lang="ts" src="./index.ts"></script>
<style lang="less" src="./index.less" scoped></style>
