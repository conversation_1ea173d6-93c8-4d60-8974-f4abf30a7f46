import PayAccountAuthBatchDialogComp from '@/application/account-auth/comp/pay-account-auth-batch-dialog/index.vue';
import UpdateNoticeDialogComp from '@/application/account-auth/comp/update-notice-dialog/index.vue';
import { GetPayAccountAuthDialogStore } from '@/application/account-auth/store';
import { StateModel } from '@/application/account-auth/types';
import { PaasPostMessage } from '@paas/paas-library';
import { defineComponent, onMounted, reactive, ref, toRefs } from 'vue';

export default defineComponent({
	components: {
		PayAccountAuthBatchDialogComp,
		UpdateNoticeDialogComp
	},
	setup() {
		const state = reactive<StateModel>({
			loading: false
		});

		const components = {
			payAccountAuthBatchDialogRef: ref<typeof PayAccountAuthBatchDialogComp>(null),
			updateNoticeDialogRef: ref<typeof UpdateNoticeDialogComp>(null)
		};

		const methods = {
			open() {
				state.loading = true;
				GetPayAccountAuthDialogStore.request()
					.getData()
					.then(data => {
						if (!data.value) {
							PaasPostMessage.post('popup.close');
							return;
						}

						components.payAccountAuthBatchDialogRef.value.show();
					})
					.catch(() => {
						PaasPostMessage.post('popup.close');
					})
					.finally(() => {
						state.loading = false;
					});
			},
			onAgree() {
				components.payAccountAuthBatchDialogRef.value.show();
			},
			onClose() {
				console.log(47);
				PaasPostMessage.post('popup.close');
			}
		};

		onMounted(() => {
			// components.updateNoticeDialogRef.value.show();
			methods.open();
		});

		return {
			...toRefs(state),
			...components,
			...methods
		};
	}
});
