import { StateModel } from '@/application/account-auth/comp/update-notice-dialog/types';
import { PaasPostMessage } from '@paas/paas-library';
import { defineComponent, reactive, toRefs } from 'vue';

export default defineComponent({
	setup(props, { emit }) {
		const state = reactive<StateModel>({
			visible: false
		});

		const methods = {
			show() {
				state.visible = true;
			},
			hide() {
				state.visible = false;
			},
			onClick(type: boolean) {
				methods.hide();

				if (type) {
					emit('agree');
				} else {
					PaasPostMessage.post('popup.close');
				}
			}
		};

		return {
			...toRefs(state),
			...methods
		};
	}
});
