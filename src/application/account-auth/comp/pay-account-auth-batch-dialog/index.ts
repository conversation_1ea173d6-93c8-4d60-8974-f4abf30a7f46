import { EmitModel, StateModel } from '@/application/account-auth/comp/pay-account-auth-batch-dialog/types';
import PayAccountAuthBatchComp from '@/components/pay-account-auth-batch/index.vue';
import { CloseOutlined } from '@ant-design/icons-vue';
import { defineComponent, reactive, toRefs } from 'vue';

export default defineComponent({
	components: {
		CloseOutlined,
		PayAccountAuthBatchComp
	},
	setup(props, { emit }: { emit: EmitModel }) {
		const state = reactive<StateModel>({
			visible: false
		});

		const methods = {
			show() {
				state.visible = true;
			},
			onSubmitAfter() {
				methods.hide();
			},
			onClose() {
				methods.hide();
			},
			hide() {
				state.visible = false;
				emit('close');
			}
		};

		return {
			...toRefs(state),
			...methods
		};
	}
});
