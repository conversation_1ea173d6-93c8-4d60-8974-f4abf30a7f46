import { defineComponent, toRefs, PropType, reactive } from 'vue';
import { MUtils, MESSAGE_TYPE, PaasPostMessage } from '@paas/paas-library';
import { useUserProfileStore } from '@/pinia/user-profile/index';
import { GetJiaxiaoConfigStore, GetShowPhoneRecordStore } from '@/utils/store';
import {
	PostEnrollPhoneStore,
	PostEnrollTelStore,
	GetSendVerifyCodeStore,
	PostEnrollSparePhoneStore,
	PostEnrollSpareTelStore
} from './store';
import { JiaxiaoPhoneLeadResponse } from '@/application/phone-rights-package/types';
import { PhoneTypeEnum, SMS_TIME } from './constant';
import { StateModel } from './types';
import type { Rule } from 'ant-design-vue/es/form';
import { Form } from 'ant-design-vue';
const useForm = Form.useForm;

export default defineComponent({
	props: {
		jiaxiaoPhoneLeadInfo: {
			type: Object as PropType<JiaxiaoPhoneLeadResponse>,
			required: true
		}
	},
	setup(props, { emit }) {
		const userProfile = useUserProfileStore().data;
		let timer = null; // 验证码倒计时
		// 获取用户信息
		const state = reactive<StateModel>({
			confirmLoading: false,
			visible: false,
			title: '设置副招生电话',
			phoneFlag: false, // 判断是否是副号
			formData: {
				phoneType: PhoneTypeEnum.TYPE_PHONE, // 招生电话类型
				phone: '', // 招生电话，手机号码
				verifyCode: '' // 验证码
			},
			smsLoading: false, // 验证码loading
			smsTimeFlag: false, // 验证码按钮禁用
			smsTime: SMS_TIME // 验证码倒计时时长
		});

		const constants = {
			PhoneTypeEnum
		};

		const methods = {
			// 显示弹框
			show(phoneFlag: boolean): void {
				state.visible = true;
				state.phoneFlag = phoneFlag;
				// 副号或者主号时取不同的字段
				if (phoneFlag) {
					state.title = '设置副招生电话';
					state.formData.phone = props.jiaxiaoPhoneLeadInfo.sparePhoneMask;
				} else {
					state.title = '设置主招生电话';
					state.formData.phone = props.jiaxiaoPhoneLeadInfo.phoneMask;
				}
			},
			// 隐藏弹框
			hide(): void {
				// 清空数据
				state.confirmLoading = false;
				state.title = '设置副招生电话';
				state.phoneFlag = false; // 判断是否是副号
				state.smsLoading = false; // 验证码loading
				state.smsTimeFlag = false; // 验证码按钮禁用
				state.smsTime = SMS_TIME; // 验证码倒计时时长
				state.formData = {
					phoneType: PhoneTypeEnum.TYPE_PHONE, // 招生电话类型
					phone: '', // 招生电话
					verifyCode: '' // 验证码
				};
				clearInterval(timer);
				state.visible = false;
			},
			// 获取验证码
			handleGetCode(): void {
				validate(['phone']).then(() => {
					if (state.smsLoading) {
						return;
					}
					state.smsLoading = true;
					GetSendVerifyCodeStore.request({
						phone: state.formData.phone,
						operator: userProfile.name,
						operatorId: userProfile.jiaxiaoId
					})
						.getData()
						.then(() => {
							state.smsLoading = false;
							methods.setCodeTimer();
						})
						.catch(() => {
							state.smsLoading = false;
						});
				});
			},
			// 获取验证码倒计时
			setCodeTimer(): void {
				const codeTimer = setInterval(() => {
					if (state.smsTime <= 1) {
						clearInterval(timer);
						state.smsTimeFlag = false;
						state.smsTime = SMS_TIME;
						return;
					}
					state.smsTime -= 1;
				}, 1000);
				state.smsTimeFlag = true;
				timer = codeTimer;
			},
			// 确定
			confirm() {
				validate().then(() => {
					let fn = null;
					if (state.phoneFlag === true) {
						fn =
							state.formData.phoneType === PhoneTypeEnum.TYPE_PHONE
								? PostEnrollSparePhoneStore
								: PostEnrollSpareTelStore;
					} else {
						fn =
							state.formData.phoneType === PhoneTypeEnum.TYPE_PHONE
								? PostEnrollPhoneStore
								: PostEnrollTelStore;
					}
					state.confirmLoading = true;
					fn.request({
						...state.formData,
						id: props.jiaxiaoPhoneLeadInfo.id,
						operator: userProfile.name,
						operatorId: userProfile.jiaxiaoId
					})
						.getData()
						.then(() => {
							state.confirmLoading = false;
							methods.hide();
							MUtils.toast('申请成功！', MESSAGE_TYPE.success);
							emit('getJiaxiaoPhoneLeadInfo');
						})
						.catch(() => {
							state.confirmLoading = false;
						});
				});
			},
			// 手机号码/固话校验，有缓存，使用state.formData取值
			validatorPhone(_rule: Rule): Promise<void> {
				if (!state.formData.phone) {
					return Promise.reject(new Error('请输入招生电话'));
				}
				if (state.formData.phoneType === PhoneTypeEnum.TYPE_PHONE) {
					if (!/^1\d{10}$/.test(state.formData.phone)) {
						return Promise.reject(new Error('招生电话格式不正确'));
					}
				} else if (state.formData.phoneType === PhoneTypeEnum.TYPE_TEL) {
					if (state.formData.phone.length < 8 || !/^\d+$/.test(state.formData.phone)) {
						return Promise.reject(new Error('招生电话格式不正确'));
					}
				}
				return Promise.resolve();
			},
			// 验证码校验，有缓存，使用state.formData取值
			validatorCode(_rule: Rule): Promise<void> {
				// 固话时不校验验证码
				if (state.formData.phoneType === PhoneTypeEnum.TYPE_TEL) {
					return Promise.resolve();
				}
				if (!state.formData.verifyCode) {
					return Promise.reject(new Error('请输入验证码'));
				}
				if (state.formData.verifyCode.length !== 6) {
					return Promise.reject(new Error('验证码格式不正确'));
				} else {
					return Promise.resolve();
				}
			},

			// 跳转到线索跟进页面
			goFollowPage(): void {
				methods.hide();
				PaasPostMessage.post('base://get.path-jump', {
					id: 'W14010102',
					appName: 'lincoln'
				});
			},

			// 切换tab回电话回拨
			changeCurrentTab(): void {
				Promise.all([
					GetShowPhoneRecordStore.request().getData(),
					GetJiaxiaoConfigStore.request().getData()
				]).then(([resPhoneRecord, resJiaxiaoConfig]) => {
					// 电话回拨开关和电话线索的开通状态同时为true才显示
					if (resPhoneRecord.value && resJiaxiaoConfig.phoneClueService) {
						methods.hide();
						PaasPostMessage.post('base://get.path-jump', {
							id: 'W14010105',
							appName: 'lincoln',
							query: {
								tab: 'historyPhone'
							}
						});
					} else {
						MUtils.toast('暂无权限，请联系管理员', MESSAGE_TYPE.error);
					}
				});
			}
		};

		const rules = reactive({
			phone: [{ required: true, validator: methods.validatorPhone, trigger: 'blur' }],
			verifyCode: [{ required: true, validator: methods.validatorCode, trigger: 'blur' }]
		});

		//表单校验
		const { validate, validateInfos } = useForm(state.formData, rules);

		return {
			...toRefs(state),
			...constants,
			...methods,
			validateInfos
		};
	}
});
