<template>
	<pm-dialog
		:confirmLoading="confirmLoading"
		v-model:visible="visible"
		:title="title"
		width="700px"
		centered
		:onClose="hide"
		@confirm="confirm"
	>
		<div>
			<div class="lead-info">
				每月仅有{{ jiaxiaoPhoneLeadInfo.limitModifyCount }}次修改机会，每月修改超过{{
					jiaxiaoPhoneLeadInfo.limitModifyCount
				}}次后无法再修改，本月已修改{{ jiaxiaoPhoneLeadInfo.modifiedCount }}次
			</div>
			<m-form
				:model="formData"
				ref="formRef"
				:label-col="{ style: { width: '100px' } }"
				:wrapper-col="{ span: 24 }"
				autocomplete="off"
			>
				<m-form-item label="招生电话类型">
					<m-radio-group v-model:value="formData.phoneType">
						<m-radio :value="PhoneTypeEnum.TYPE_PHONE">手机号码</m-radio>
						<m-radio :value="PhoneTypeEnum.TYPE_TEL">固话</m-radio>
					</m-radio-group>
					<span style="color: var(--text-secondary)">（请添加区号，如：02387908888）</span>
				</m-form-item>
				<m-form-item label="招生电话" v-bind="validateInfos.phone">
					<m-input
						v-model:value="formData.phone"
						:maxlength="formData.phoneType === PhoneTypeEnum.TYPE_PHONE ? 11 : 12"
						placeholder="请输入招生电话"
					/>
				</m-form-item>
				<m-form-item
					v-if="formData.phoneType === PhoneTypeEnum.TYPE_PHONE"
					label="验证码"
					v-bind="validateInfos.verifyCode"
				>
					<div style="display: flex">
						<m-input
							style="border-radius: 4px 0 0 4px"
							v-model:value="formData.verifyCode"
							placeholder="请输入验证码"
						/>
						<m-button
							style="border-radius: 0 4px 4px 0"
							:loading="smsLoading"
							:disabled="smsTimeFlag"
							@click="handleGetCode"
							type="primary"
						>
							{{ !smsTimeFlag ? '获取验证码' : '重新获取' + smsTime + 's' }}
						</m-button>
					</div>
				</m-form-item>
			</m-form>
			<div class="set-phone-tip">
				<div v-if="formData.phoneType === PhoneTypeEnum.TYPE_TEL" class="tel-tip">
					(请输入8-12位的数字，固话请添加区号，如：02387908888)
				</div>
				<div class="tip-ul">
					<ul>
						<li>注：</li>
						<li>1.为保护双方隐私，我司已开启“号码保护”防止个人信息泄露。</li>
						<li>
							2.驾校招生电话将以隐私号的形式展示在驾考宝典，学员拨打驾校电话和驾校接收学员电话时只能看到对方的虚拟号码。
						</li>
						<li>
							3.驾校可设置主副两个招生电话，默认转接到主招生电话，当学员第一次拨打主招生电话未接通时，自动转接到另一个招生电话，当两个整体接通率低于70%时，随机切换招生电话接通，请保持招生电话畅通。
						</li>
						<li>
							4.您接通的电话可在
							<span @click="goFollowPage" class="active">线索跟进列表</span>
							查询学员真实的手机号码，未接通的电话可在
							<span @click="changeCurrentTab" class="active">电话回拨列表</span>
							再次联系学员
						</li>
					</ul>
				</div>
			</div>
		</div>
	</pm-dialog>
</template>

<script lang="ts" src="./index.ts"></script>
<style lang="less" src="./index.less" scoped></style>
