export interface EnrollPhoneModel {
	phoneType?: string; // 招生电话类型
	phone: string; // 招生电话
	verifyCode?: string; // 验证码
	operator: string; // 当前操作驾校
	operatorId: number; // 当前操作驾校ID
}

export interface StateModel {
	confirmLoading: boolean;
	visible: boolean;
	title: string;
	phoneFlag: boolean;
	formData: FormDataModel;
	smsLoading: boolean;
	smsTimeFlag: boolean;
	smsTime: number;
}
export interface FormDataModel {
	phoneType: string; // 招生电话类型
	phone: string; // 招生电话，手机号码
	verifyCode: string; // 验证码
}
