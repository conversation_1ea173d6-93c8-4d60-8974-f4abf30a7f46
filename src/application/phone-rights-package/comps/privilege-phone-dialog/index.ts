import { defineComponent, reactive, toRefs } from 'vue';
import { PaasPostMessage } from '@paas/paas-library';
import SettingAuthComp from '@/application/phone-rights-package/comps/setting-auth/index.vue';
import SettingPhoneComp from '@/application/phone-rights-package/comps/setting-phone/index.vue';
import SettingFinishComp from '@/application/phone-rights-package/comps/setting-finish/index.vue';
import { StateModel } from './types';

export default defineComponent({
	components: {
		SettingAuthComp,
		SettingPhoneComp,
		SettingFinishComp
	},
	setup(props, { emit }) {
		const state = reactive<StateModel>({
			visible: false,
			current: 0,
			orderNumber: ''
		});

		// 方法
		const methods = {
			show(orderNumber, current) {
				console.log(orderNumber, current);
				state.visible = true;
				state.orderNumber = orderNumber;
				state.current = current;
			},
			close() {
				state.visible = false;
				PaasPostMessage.post('main://navigation.to', 'W3201');
				PaasPostMessage.post('main://tab.close', 'buick.H3224');
			},
			nextStep() {
				state.current++;
			},
			handleBuy(buyAgain) {
				state.visible = false;
				emit('handleBuy', buyAgain);
			}
		};

		return {
			...toRefs(state),
			...methods
		};
	}
});
