.setting-finish {
	margin-top: 30px;
	.status {
		display: flex;
		justify-content: center;
		align-items: center;
		.status-success {
			width: 32px;
			height: 32px;
			background: url('../../img/icon_success.png') no-repeat center;
			background-size: 100%;
		}
		.status-text {
			margin-left: 12px;
			font-size: 22px;
			font-weight: 500;
		}
	}
	.container {
		margin-top: 20px;
		.remark {
			text-align: center;
			color: rgba(0, 0, 0, 0.65);
		}
	}
	.bg-left {
		margin: 20px auto;
		width: 450px;
		height: 214px;
		background: url('../../img/bg_finish_left.png') no-repeat center;
		background-size: 100%;
	}
	.bg-right {
		margin: 20px auto;
		width: 450px;
		height: 214px;
		background: url('../../img/bg_finish_right.png') no-repeat center;
		background-size: 100%;
	}
	.btns {
		margin-top: 20px;
		display: flex;
		justify-content: center;
		.margin-left-20 {
			margin-left: 20px;
		}
	}
}
