import { defineComponent } from 'vue';

import { PaasPostMessage } from '@paas/paas-library';

export default defineComponent({
	setup() {
		const methods = {
			// 跳转到班型页面
			jumpCoursePage() {
				PaasPostMessage.post('base://get.path-jump', {
					id: 'W340207',
					appName: 'jaguar'
				});
			},
			// 跳转到训练场页面
			jumpTrainingPage() {
				PaasPostMessage.post('base://get.path-jump', {
					id: 'W340208',
					appName: 'jaguar'
				});
			}
		};

		return {
			...methods
		};
	}
});
