<template>
	<div class="setting-phone">
		<m-row class="steps" :gutter="20">
			<m-col :span="12">
				<div class="box">
					<div class="title">电话线索接收设置</div>
					<div class="box-phone">
						<div class="phone-content">
							<div class="settings">
								<ul class="list" v-if="jiaxiaoPhoneLeadInfo">
									<li class="phone">
										<span>招生电话：</span>
										<div class="phone-info">
											<div class="flex">
												<span class="label bg-error">主</span>
												<span
													v-if="jiaxiaoPhoneLeadInfo.status === PhoneLeadStatusEnum.APPLYING"
												>
													{{ jiaxiaoPhoneLeadInfo.phoneMask }} 申请中
												</span>
												<span
													v-else-if="
														jiaxiaoPhoneLeadInfo.status === PhoneLeadStatusEnum.REVIEW
													"
												>
													{{ jiaxiaoPhoneLeadInfo.newPhoneMask }} 审核中
												</span>
												<span
													v-else-if="
														[
															PhoneLeadStatusEnum.NOT1,
															PhoneLeadStatusEnum.NOT2,
															PhoneLeadStatusEnum.NOT3,
															PhoneLeadStatusEnum.NOT4,
															PhoneLeadStatusEnum.SUCCESS
														].includes(jiaxiaoPhoneLeadInfo.status)
													"
												>
													{{ jiaxiaoPhoneLeadInfo.phoneMask }}
													<m-button type="link" @click="setPhone(false)">
														{{ jiaxiaoPhoneLeadInfo.phoneMask ? '修改' : '设置' }}
													</m-button>
													<m-button
														type="link"
														v-if="
															jiaxiaoPhoneLeadInfo.phoneMask &&
															jiaxiaoPhoneLeadInfo.sparePhoneMask
														"
														@click="delPhone(false)"
													>
														删除
													</m-button>
												</span>
											</div>
											<div v-if="jiaxiaoPhoneLeadInfo.lastRefusePhoneMask" class="new-phone">
												新电话({{ jiaxiaoPhoneLeadInfo.lastRefusePhoneMask }})未通过
											</div>
										</div>
									</li>
									<li class="phone" v-if="jiaxiaoPhoneLeadInfo.phoneMask">
										<div class="phone-info">
											<span class="label bg-warning" style="">副</span>
											<span>
												{{ jiaxiaoPhoneLeadInfo.sparePhoneMask }}
												<m-button type="link" @click="setPhone(true)">
													{{ jiaxiaoPhoneLeadInfo.sparePhoneMask ? '修改' : '设置' }}
												</m-button>
												<m-button
													type="link"
													v-if="jiaxiaoPhoneLeadInfo.sparePhoneMask"
													@click="delPhone(true)"
												>
													删除
												</m-button>
											</span>
										</div>
									</li>
									<li>
										<span v-if="jiaxiaoPhoneLeadInfo.privatePhone" class="privacy-number">
											{{ jiaxiaoPhoneLeadInfo.privatePhone }}
											<label>隐</label>
										</span>
									</li>
									<li
										v-if="jiaxiaoPhoneLeadInfo.status !== PhoneLeadStatusEnum.NOT1"
										class="open-phone-clue"
									>
										<span class="text">接收电话线索</span>
										<m-switch :checked="statusChecked" @change="onSetPhoneClue" />
										<m-tooltip>
											<template #title>
												关闭此开关，设置的招生电话将失效，电话线索将不再推送
											</template>
											<question-circle-filled />
										</m-tooltip>
									</li>
								</ul>
							</div>
						</div>
					</div>
					<div class="description">驾校可设置主副两个招生电话，将根据接通率自动切换</div>
					<div class="msg">设置招生电话后，学员可在驾考宝典APP直接联系驾校</div>
					<div class="bg-left"></div>
				</div>
			</m-col>
			<m-col :span="12">
				<div class="box">
					<div class="title">电话漏接提醒</div>
					<div class="description">打开微信漏接提醒，及时回拨学员电话</div>
					<div class="msg">先微信扫码关注公众号，然后扫码绑定小程序通知—点击微信绑定，绑定用户信息</div>
					<div class="bg-right"></div>
				</div>
			</m-col>
		</m-row>
		<div class="btns">
			<m-button type="primary" :loading="loading" @click="nextStep">下一步</m-button>
		</div>
	</div>
	<setting-phone-dialog-comp
		ref="settingPhoneDialogCompRef"
		:jiaxiao-phone-lead-info="jiaxiaoPhoneLeadInfo"
		@getJiaxiaoPhoneLeadInfo="onGetJiaxiaoPhoneLeadInfo"
	/>
</template>

<script lang="ts" src="./index.ts"></script>
<style lang="less" src="./index.less" scoped></style>
