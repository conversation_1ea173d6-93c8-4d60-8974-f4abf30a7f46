.setting-phone {
	margin-top: 30px;
	.box {
		height: 500px;
		background: rgba(30, 123, 249, 0.05);
		border-radius: 4px;
		padding: 15px 20px;
		.title {
			font-size: 16px;
			font-weight: 500;
		}
		.box-phone {
			margin-top: 5px;
		}
		.description {
			margin-top: 10px;
			color: rgba(0, 0, 0, 0.65);
		}
		.msg {
			margin-top: 20px;
			font-size: 16px;
		}
		.bg-left {
			margin: 20px auto;
			width: 370px;
			height: 256px;
			background: url('../../img/bg_left.png') no-repeat center;
			background-size: 100%;
		}
		.bg-right {
			margin: 20px auto;
			width: 340px;
			height: 196px;
			background: url('../../img/bg_right.png') no-repeat center;
			background-size: 100%;
		}
	}
	:deep(.btns) {
		margin-top: 30px;
		display: flex;
		justify-content: center;
		.ant-btn {
			font-size: 16px;
			color: #ffffff;
			padding: 9px 68px;
			height: auto;
		}
	}
	.phone-content {
		display: flex;
		align-items: center;
		.settings {
			display: flex;
			flex-direction: column;

			.list {
				font-size: 14px;
				color: #0a1639;
				line-height: 32px;
				padding-left: 0;
				margin-bottom: 0;

				> li {
					float: left;
					margin-right: 30px;

					.m-button {
						font-size: 14px;
					}

					&.phone {
						display: flex;
						align-items: center;

						.phone-info {
							display: flex;
							align-items: center;

							.ant-btn-link {
								padding: 4px;
							}

							.label {
								font-size: 12px;
								width: 16px;
								height: 16px;
								text-align: center;
								line-height: 16px;
								color: #fff;
								border-radius: 2px;
								display: inline-block;
								margin-right: 4px;
							}
							.new-phone {
								line-height: 14px;
								color: #e6a23c;
							}

							&.wx-info {
								display: flex;
								align-items: center;

								.tag {
									font-size: 12px;
									font-weight: 400;
									text-align: center;
									color: #ffffff;
									line-height: 16px;
									padding: 2px 6px;
									background: var(--ant-primary-color);
									border-radius: 2px;
									margin-left: 10px;

									&.disabled {
										background: var(--border-color-base);
									}
								}
							}
						}
					}

					.privacy-number {
						display: flex;
						align-items: center;

						label {
							font-size: 12px;
							width: 16px;
							height: 16px;
							text-align: center;
							line-height: 16px;
							color: #fff;
							background: #faad14;
							border-radius: 2px;
							display: inline-block;
							margin-left: 4px;
						}
					}

					&.open-phone-clue {
						display: flex;
						align-items: center;
						height: 32px;

						.text {
							font-size: 14px;
							font-weight: 400;
							color: var(--text-primary);
							line-height: 22px;
							margin-right: 7px;
						}

						&:deep(.anticon) {
							color: #faad14;
							line-height: 20px;
							padding-top: 3px;
							padding-left: 10px;
							font-size: 16px;
						}
					}
				}
			}

			.tips {
				font-size: 12px;
				font-weight: 400;
				text-align: left;
				color: var(--ant-warning-color);
				line-height: 17px;
				margin: 6px 0 0 0;
			}
		}

		.preview {
			.ant-btn {
				font-size: 14px;
				font-weight: 400;
				text-align: center;
				color: var(--ant-primary-color);
				line-height: 20px;
				padding: 4px 10px;
				border: 1px solid var(--ant-primary-color);
				border-radius: 2px;
				background-color: transparent;

				&:hover {
					background-color: transparent;
				}
			}
		}
	}
	.flex {
		display: flex;
		align-items: center;
	}
	.bg-error {
		background-color: var(--ant-error-color);
	}
	.bg-warning {
		background-color: #faad14;
	}
}
