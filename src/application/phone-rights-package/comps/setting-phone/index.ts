import { defineComponent, onMounted, reactive, toRefs, computed, ref } from 'vue';
import { MESSAGE_TYPE, MUtils } from '@paas/paas-library';
import { QuestionCircleFilled } from '@ant-design/icons-vue';
import { useUserProfileStore } from '@/pinia/user-profile';
import {
	FetchClosePhoneClueStore,
	FetchDelPhoneStore,
	FetchOpenPhoneClueStore,
	GetJiaxiaoPhoneLeadStore
} from '@/application/phone-rights-package/store';
import { PhoneLeadStatusEnum } from '@/application/phone-rights-package/constants';
import SettingPhoneDialogComp from '@/application/phone-rights-package/comps/setting-phone-dialog/index.vue';
import { StateModel } from './types';

export default defineComponent({
	components: {
		QuestionCircleFilled,
		SettingPhoneDialogComp
	},
	setup(props, { emit }) {
		const userProfileStore = useUserProfileStore();

		const state = reactive<StateModel>({
			loading: false,
			jiaxiaoPhoneLeadInfo: null
		});

		const constants = {
			PhoneLeadStatusEnum
		};

		const computeds = {
			statusChecked: computed(() => {
				return state.jiaxiaoPhoneLeadInfo?.status === PhoneLeadStatusEnum.SUCCESS;
			})
		};

		// 页面组件实例集合
		const components = {
			settingPhoneDialogCompRef: ref<typeof SettingPhoneDialogComp>()
		};

		const methods = {
			// 获取电话线索服务的状态
			async onGetJiaxiaoPhoneLeadInfo() {
				if (!userProfileStore.isSuccess) {
					await userProfileStore.fetchUserProfile();
				}
				const jiaxiaoId = userProfileStore.data?.jiaxiaoId;
				if (!jiaxiaoId) {
					return;
				}
				// 驾校电话线索信息
				state.jiaxiaoPhoneLeadInfo = await GetJiaxiaoPhoneLeadStore.request({ jiaxiaoId }).getData();
			},
			// 设置，修改招生电话，phoneFlag判断是否是副号
			setPhone(phoneFlag: boolean) {
				components.settingPhoneDialogCompRef.value.show(phoneFlag);
			},
			// 删除招生电话
			delPhone(phoneFlag: boolean) {
				const id = state.jiaxiaoPhoneLeadInfo.id;
				if (!id) {
					return;
				}
				const content = phoneFlag ? '确认删除？' : '删除后，副招生号码将变成主号码，确认删除？';
				MUtils.confirm({
					title: '温馨提示',
					content,
					type: MESSAGE_TYPE.warning
				}).then(bool => {
					if (!bool) {
						return;
					}
					state.loading = true;
					FetchDelPhoneStore.request({
						id,
						bindPhoneType: phoneFlag ? 'SPARE' : 'MASTER'
					})
						.getData()
						.then(() => {
							MUtils.toast('删除成功', MESSAGE_TYPE.success);
							methods.onGetJiaxiaoPhoneLeadInfo();
						})
						.finally(() => {
							state.loading = false;
						});
				});
			},
			// 电话线索服务开关
			onSetPhoneClue() {
				MUtils.confirm({
					title: '温馨提示',
					content: '确认修改？',
					type: MESSAGE_TYPE.warning
				}).then(bool => {
					if (!bool) {
						return;
					}
					if (computeds.statusChecked.value) {
						// 关闭
						const jiaxiaoId = userProfileStore.data?.jiaxiaoId;
						if (!jiaxiaoId) {
							return;
						}
						state.loading = true;
						FetchClosePhoneClueStore.request({ jiaxiaoId })
							.getData()
							.then(() => {
								MUtils.toast('修改成功', MESSAGE_TYPE.success);
								methods.onGetJiaxiaoPhoneLeadInfo();
							})
							.finally(() => {
								state.loading = false;
							});
					} else {
						// 开启
						const id = state.jiaxiaoPhoneLeadInfo.id;
						if (!id) {
							return;
						}
						state.loading = true;
						FetchOpenPhoneClueStore.request({
							id
						})
							.getData()
							.then(() => {
								MUtils.toast('修改成功', MESSAGE_TYPE.success);
								methods.onGetJiaxiaoPhoneLeadInfo();
							})
							.finally(() => {
								state.loading = false;
							});
					}
				});
			},
			// 下一步
			nextStep() {
				if (state.jiaxiaoPhoneLeadInfo.privatePhone) {
					emit('nextStep');
				} else {
					MUtils.toast('请先设置招生电话并且打开接收电话线索开关', MESSAGE_TYPE.warning);
				}
			}
		};

		onMounted(() => {
			methods.onGetJiaxiaoPhoneLeadInfo();
		});

		return {
			...toRefs(state),
			...computeds,
			...constants,
			...components,
			...methods
		};
	}
});
