import { CONNECT_OPTIONS } from '@/components/pay-account-auth-dialog/constants';
import { FetchPayAccountAuthPayNumberStore } from '@/components/pay-account-auth-dialog/store';
import { PayAccountAuthParamsModel, StateModel } from '@/components/pay-account-auth-dialog/types';
import { validateName } from '@/components/pay-account-auth-dialog/utils';
import { useOpenPayAccountAuthStore } from '@/pinia/open-pay-account-auth';
import { AuthResultEnum } from '@/pinia/open-pay-account-auth/constants';
import { validatePhone } from '@/utils/util-validate';
import { ExclamationCircleFilled } from '@ant-design/icons-vue';
import { PaasPostMessage } from '@paas/paas-library';
import type { Rule } from 'ant-design-vue/es/form';
import { defineComponent, reactive, toRefs } from 'vue';
import { GetPaySuccessStore } from '@/application/phone-rights-package/store';
import { ActionEnum } from '@/utils/constants/common';

export default defineComponent({
	props: {
		orderNumber: {
			type: String,
			required: true
		}
	},
	components: {
		ExclamationCircleFilled
	},
	setup(props, { emit }) {
		const openPayAccountAuthStore = useOpenPayAccountAuthStore();

		const INIT = -1;

		const state = reactive<StateModel>({
			authLoading: false,
			buyTimeout: false,
			// 上一次的数据
			lastData: {
				status: INIT,
				amount: null,
				typeShow: '',
				account: ''
			},
			formState: {
				name: '',
				phone: '',
				relationship: undefined
			}
		});

		const constants = {
			CONNECT_OPTIONS,
			ActionEnum,
			AuthResultEnum,
			INIT
		};

		const methods = {
			onFinish() {
				state.lastData.amount = state.lastData.amount || openPayAccountAuthStore.amount;
				state.lastData.typeShow = state.lastData.typeShow || openPayAccountAuthStore.typeShow;
				state.lastData.account = state.lastData.account || openPayAccountAuthStore.account;

				const params: PayAccountAuthParamsModel = {
					...state.formState,
					payNumber: props.orderNumber
				};

				state.authLoading = true;
				FetchPayAccountAuthPayNumberStore.request(params)
					.getData()
					.then(async data => {
						const id = data.value;

						await openPayAccountAuthStore.fetchStatus({
							id
						});
						if (openPayAccountAuthStore.status === AuthResultEnum.SUCCESS) {
							methods.getPaySuccess();
						} else {
							state.authLoading = false;
							state.lastData.status = openPayAccountAuthStore.status;
							if (openPayAccountAuthStore.status === AuthResultEnum.FAIL) {
								openPayAccountAuthStore.$reset();
								openPayAccountAuthStore.clearTimer();
							}
						}
					})
					.catch(() => {
						state.authLoading = false;
					});
			},
			onClose() {
				methods.reset();
				openPayAccountAuthStore.$reset();
				openPayAccountAuthStore.clearTimer();
			},
			reset() {
				state.buyTimeout = false;
				state.lastData = {
					status: INIT,
					amount: null,
					typeShow: '',
					account: ''
				};
				state.formState = {
					name: '',
					phone: '',
					relationship: undefined
				};
			},
			// 跳转到商城中心
			jumpGoodsCenterPage() {
				PaasPostMessage.post('base://get.path-jump', {
					id: 'W3201',
					appName: 'buick'
				});
			},
			handleBuy() {
				emit('handleBuy', true);
			},
			// 轮询是否需要打开引导弹框，设置手机号
			getPaySuccess() {
				let counter = 0;
				const timer = setInterval(async () => {
					const result = await GetPaySuccessStore.request({
						orderNo: props.orderNumber
					}).getData();
					counter++;
					if (counter >= 10) {
						state.authLoading = false;
						clearInterval(timer);
						methods.onClose();
						state.buyTimeout = true;
					}
					if (result.value) {
						state.authLoading = false;
						clearInterval(timer);
						// MUtils.toast('购买成功', MESSAGE_TYPE.success);
						methods.onClose();
						emit('nextStep');
					}
				}, 1000);
			}
		};

		const rules: Record<string, Rule[]> = {
			name: [{ required: true, validator: validateName, trigger: 'blur' }],
			phone: [{ required: true, validator: validatePhone, trigger: 'blur' }],
			relationship: [{ required: true, message: '请选择充值账户与绑定驾校的关系', trigger: 'change' }]
		};

		return {
			openPayAccountAuthStore,
			rules,
			...toRefs(state),
			...constants,
			...methods
		};
	}
});
