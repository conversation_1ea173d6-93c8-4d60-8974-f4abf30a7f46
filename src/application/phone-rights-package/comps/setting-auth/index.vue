<template>
	<div class="setting-auth">
		<div v-if="buyTimeout">
			<div class="status">
				<div class="status-warning"></div>
				<div class="status-text">购买超时</div>
			</div>
			<div class="container">
				<p class="remark">
					由于支付账户长时间未授权或授权失败，商品未购买成功，可能已经自动退款，请关注交易明细，若您已收到退款，可选择重新购买
				</p>
			</div>
			<div class="btns">
				<m-button type="primary" @click="jumpGoodsCenterPage">查看退款明细</m-button>
				<m-button class="margin-left-20" type="primary" @click="handleBuy">重新购买</m-button>
			</div>
		</div>
		<div v-else-if="lastData.status === INIT || lastData.status === AuthResultEnum.FAIL">
			<div class="status" v-if="lastData.status === INIT">
				<div class="status-success"></div>
				<div class="status-text">支付成功，请尽快完成支付账户授权</div>
			</div>
			<div class="status" v-else-if="lastData.status === AuthResultEnum.FAIL">
				<div class="status-error"></div>
				<div class="status-text">授权失败</div>
			</div>
			<m-spin :spinning="openPayAccountAuthStore.statusLoading || authLoading" tip="授权结果查询中...">
				<div class="container">
					<div>
						<p class="remark" v-if="lastData.status === AuthResultEnum.FAIL">
							支付账户实名验证失败，请重新确认支付账户真实姓名
						</p>
						<p class="money" v-if="lastData.amount || openPayAccountAuthStore.amount">
							您已成功充值
							<span>{{ lastData.amount || openPayAccountAuthStore.amount }}</span>
							元
						</p>
						<p class="remark-left" v-if="lastData.status === INIT">
							由于该充值的账户未与当前驾校绑定，充值后金额将冻结，为保证您的充值信息安全，请补充充值账户相关信息，平台根据您填写的信息打款验证后，会自动绑定本次充值用户为您当前驾校的可充值用户，后期可正常充值
						</p>
					</div>

					<ul class="info">
						<li>
							<label>本次支付账户类型：</label>
							{{ lastData.typeShow || openPayAccountAuthStore.typeShow }}
						</li>
						<li v-if="lastData.account || openPayAccountAuthStore.account">
							<label>本次支付账户：</label>
							{{ lastData.account || openPayAccountAuthStore.account }}
						</li>
					</ul>
					<div class="content">
						<h3>需补充的充值账户信息如下：</h3>
						<m-form
							:model="formState"
							:rules="rules"
							:label-col="{ style: { width: '240px' } }"
							:wrapper-col="{ span: 24 }"
							autocomplete="off"
							@finish="onFinish"
						>
							<m-form-item label="充值账户真实姓名" name="name">
								<m-input
									class="tips-input"
									v-model:value="formState.name"
									:maxlength="20"
									placeholder="请输入您的真实姓名"
								/>
								<p class="tips">
									<exclamation-circle-filled />
									该姓名用于转账验证，请按真实姓名填写，填写错误将转账失败
								</p>
							</m-form-item>
							<m-form-item label="充值账户手机号" name="phone">
								<m-input
									v-model:value="formState.phone"
									:maxlength="11"
									placeholder="请输入您的11位手机号"
								/>
							</m-form-item>
							<m-form-item label="充值账户与绑定驾校的关系" name="relationship">
								<m-select
									v-model:value="formState.relationship"
									:options="CONNECT_OPTIONS"
									placeholder="请选择您与驾校的关系"
								></m-select>
							</m-form-item>
							<div class="footer">
								<m-button type="primary" html-type="submit">下一步</m-button>
							</div>
						</m-form>
					</div>
				</div>
			</m-spin>
		</div>
	</div>
</template>

<script lang="ts" src="./index.ts"></script>
<style lang="less" src="./index.less" scoped></style>
