.setting-auth {
	margin-top: 30px;
	.status {
		display: flex;
		justify-content: center;
		align-items: center;
		.status-success {
			width: 32px;
			height: 32px;
			background: url('../../img/icon_success.png') no-repeat center;
			background-size: 100%;
		}
		.status-error {
			width: 32px;
			height: 32px;
			background: url('../../img/icon_error.png') no-repeat center;
			background-size: 100%;
		}
		.status-warning {
			width: 32px;
			height: 32px;
			background: url('../../img/icon_warning.png') no-repeat center;
			background-size: 100%;
		}
		.status-text {
			margin-left: 12px;
			font-size: 22px;
			font-weight: 500;
		}
	}
	.container {
		margin-top: 20px;
		.money {
			font-size: 22px;
			font-weight: 600;
			color: rgba(0, 0, 0, 0.85);
			line-height: 30px;
			margin-bottom: 10px;

			span {
				color: #1e7bf9;
			}
		}

		.remark {
			text-align: center;
			color: rgba(0, 0, 0, 0.65);
		}
		.remark-left {
			text-align: left;
			color: rgba(0, 0, 0, 0.65);
		}

		.info {
			border: 1px solid #ebeef5;
			border-radius: 2px;
			margin-top: 20px;

			li {
				font-size: 14px;
				font-weight: 400;
				color: #212121;
				line-height: 38px;
				padding-left: 20px;

				&:first-child {
					background-color: #f6f8fa;
				}

				label {
					color: #666666;
					width: 141px;
					display: inline-block;
					font-weight: 400;
				}
			}
		}

		.content {
			h3 {
				font-size: 16px;
				font-weight: 600;
				color: #39393a;
				line-height: 22px;
				margin: 30px 0 15px;
			}

			:deep(.ant-form) {
				.ant-form-item {
					.ant-form-item-label {
						text-align: left;
					}

					.ant-form-item-control {
						.ant-form-item-control-input {
							.ant-form-item-control-input-content {
								.tips-input {
									margin-bottom: 27px;
								}

								.tips {
									font-size: 14px;
									font-weight: 400;
									color: #faad14;
									line-height: 22px;
									position: absolute;
									left: -240px;
									top: 37px;

									i {
										margin-right: 5px;
									}
								}
							}
						}
					}
				}

				.footer {
					margin: 0;

					.ant-btn {
						font-size: 16px;
						font-weight: 400;
						color: #ffffff;
						line-height: 22px;
						padding: 9px 68px;
						height: auto;
						margin: 12px auto 10px;
					}
				}
			}
		}

		.footer {
			margin-top: 30px;
			margin-bottom: 30px;
			text-align: center;
		}
	}
	.btns {
		margin-top: 20px;
		display: flex;
		justify-content: center;
		.margin-left-20 {
			margin-left: 20px;
		}
	}
}
