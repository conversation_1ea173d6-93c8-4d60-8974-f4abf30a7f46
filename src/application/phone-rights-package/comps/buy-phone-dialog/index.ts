import { ItemListModel } from '@/application/phone-rights-package/types';
import { PayTypeEnum } from '@/components/pay-type/constants';
import PayTypeComp from '@/components/pay-type/index.vue';
import { useAccountInfoStore } from '@/pinia/account-info';
import { useOpenPayAccountAuthStore } from '@/pinia/open-pay-account-auth';
import { useRechargeStore } from '@/pinia/recharge';
import { BizSceneEnum, PaySubTypeEnum, RechargeStatusEnum, SourceEnum } from '@/pinia/recharge/constant';
import { StatusModel } from '@/pinia/recharge/types';
import { MESSAGE_TYPE, MUtils, PaasPostMessage } from '@paas/paas-library';
import { computed, defineComponent, reactive, ref, toRefs } from 'vue';
import { StateModel } from './types';
import { GetPaySuccessStore } from '@/application/phone-rights-package/store';

export default defineComponent({
	components: {
		PayTypeComp
	},
	setup(props, { emit }) {
		const accountInfoStore = useAccountInfoStore();
		const rechargeStore = useRechargeStore();
		const openPayAccountAuthStore = useOpenPayAccountAuthStore();

		const state = reactive<StateModel>({
			visible: false,
			loading: false,
			// 当前购买的商品信息
			currentGoodsInfo: null
		});

		const computeds = {
			// 是否展示loading
			showedLoading: computed(() => {
				const payTypeRef = components.payTypeRef.value;
				const payType = payTypeRef?.payType;

				return (
					rechargeStore.isLoading ||
					(payType === PayTypeEnum.BALANCE && rechargeStore.statusLoading) ||
					state.loading
				);
			})
		};

		const constants = {
			PayTypeEnum
		};

		const components = {
			payTypeRef: ref<typeof PayTypeComp>(null)
		};

		// 方法
		const methods = {
			show(goodsInfo: ItemListModel) {
				state.currentGoodsInfo = goodsInfo;
				state.visible = true;

				setTimeout(() => {
					if (accountInfoStore.accountAmount > state.currentGoodsInfo.price) {
						components.payTypeRef.value.payType = PayTypeEnum.BALANCE;
					}
				}, 100);
			},
			async createOrder() {
				const payTypeRef = components.payTypeRef.value;
				const payType = payTypeRef.payType as PayTypeEnum;
				payTypeRef.errorMsg = '';

				if (!payTypeRef.isShowPay) {
					return;
				}

				// 创建充值订单
				rechargeStore.$reset();
				await rechargeStore.fetchRecharge({
					amount: state.currentGoodsInfo.price,
					goodsCode: state.currentGoodsInfo.goodsCode,
					paySubType: PaySubTypeEnum[payType],
					source: SourceEnum.PAAS,
					bizScene: BizSceneEnum.LEAD_PACK_BUY
				});

				if (rechargeStore.isSuccess) {
					payTypeRef.setPayInfo({
						money: state.currentGoodsInfo.price,
						bizScene: BizSceneEnum.LEAD_PACK_BUY
					});
				}

				if (rechargeStore.isError) {
					const options = {
						title: '提示',
						content: rechargeStore.error.message,
						btText: '确定',
						type: MESSAGE_TYPE.error
					};
					MUtils.alert(options);
				}
			},

			rechargeEnd(params: StatusModel) {
				if (rechargeStore.status === RechargeStatusEnum.SUCCESS) {
					MUtils.toast('购买成功', MESSAGE_TYPE.success);
					openPayAccountAuthStore.open({
						...params,
						hideDialog: true,
						beforeCallback: res => {
							methods.close();
							if (res.isNeedAuth === true) {
								emit('showPrivilegePhoneDialog', params.orderNumber, 0);
							} else if (res.isNeedAuth === false) {
								methods.getPaySuccess(params.orderNumber);
							}
						}
					});
				} else if (rechargeStore.status === RechargeStatusEnum.FAIL) {
					MUtils.toast('购买失败', MESSAGE_TYPE.error);
				}
			},

			reset() {
				state.currentGoodsInfo = null;
			},

			close() {
				components.payTypeRef.value?.reset();
				methods.reset();
				state.visible = false;
			},

			// 轮询是否需要打开引导弹框，设置手机号
			getPaySuccess(orderNumber) {
				state.loading = true;
				let counter = 0;
				const timer = setInterval(async () => {
					const result = await GetPaySuccessStore.request({ orderNo: orderNumber }).getData();
					counter++;
					if (counter >= 10) {
						clearInterval(timer);
						MUtils.toast('购买超时', MESSAGE_TYPE.warning);
						setTimeout(() => {
							state.loading = false;
							PaasPostMessage.post('main://navigation.to', 'W3201');
							PaasPostMessage.post('main://tab.close', 'buick.H3224');
						}, 2000);
					}
					if (result.value) {
						clearInterval(timer);
						state.loading = false;
						emit('showPrivilegePhoneDialog', orderNumber, 1);
					}
				}, 1000);
			}
		};

		return {
			...toRefs(state),
			...computeds,
			...constants,
			...components,
			...methods
		};
	}
});
