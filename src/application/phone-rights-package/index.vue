<template>
	<div class="phone-rights-package">
		<div class="header-title">畅享电话卡</div>
		<div class="content">
			<div class="content-info">
				<div class="left">
					<div class="left-top">
						<div class="title">
							畅享电话卡，超值特惠仅需
							<span>{{ goodsInfo?.price }}</span>
							元
						</div>
						<m-row class="description">
							<m-col :span="12">· 一次付费，解锁全年无上限电话线索</m-col>
							<m-col :span="12">· 随时随地接收高质量线索</m-col>
							<m-col :span="12">· 驾考宝典app内驾校电话曝光</m-col>
							<m-col :span="12">· 高意向学员将电话联系您</m-col>
						</m-row>
					</div>
					<div class="left-bottom">
						<div class="title">购买说明：</div>
						<m-row class="description">
							<m-col :span="24">
								· 畅享电话卡，与会员权益、招生宝线索{{
									hideDirected ? '' : '（自动扣费定向、非定向）'
								}}不能同时共享，若您购买畅享电话卡后，又购买了会员或招生宝线索，畅享电话卡将自动失效并退单到您的账户余额，退单金额将根据剩余到期时间计算
							</m-col>
							<m-col :span="24">· 畅享电话卡退单后，电话线索将按原定价计算</m-col>
						</m-row>
					</div>
				</div>
				<div class="right"></div>
			</div>
			<div class="content-step">
				<div class="title">只需简单几步，即可解锁全年无限制的高质量通话线索</div>
				<m-row class="steps" :gutter="20">
					<m-col :span="12" v-if="authIsShow">
						<div class="header">
							<div>1、完成招生授权</div>
							<div>
								<m-button type="link" @click="jumpAuthPage">去完成</m-button>
							</div>
						</div>
						<div class="step1">
							<img src="./img/step1.png" alt="" />
						</div>
					</m-col>
					<m-col :span="12">
						<div class="header">
							<div>
								<span v-if="authIsShow">2</span>
								<span v-else>1</span>
								、绑定招生电话，可设置主、副两个招生号码。将根据接通率接入不同的号码
							</div>
						</div>
						<div class="step2">
							<img src="./img/step2.png" alt="" />
						</div>
					</m-col>
				</m-row>
				<m-row class="steps" :gutter="20">
					<m-col :span="12">
						<div class="header">
							<div>
								<span v-if="authIsShow">3</span>
								<span v-else>2</span>
								、完善班型、训练场信息，将提升驾校排名曝光，增加电话线索接收量
							</div>
						</div>
						<div class="step3">
							<img src="./img/step3.png" alt="" />
						</div>
					</m-col>
					<m-col :span="12">
						<div class="header"></div>
						<div class="step4">
							<img src="./img/step4.png" alt="" />
						</div>
					</m-col>
				</m-row>
			</div>
		</div>
		<div class="pay-button">
			<m-button type="primary" @click="handleBuy">¥{{ goodsInfo?.price }} 购买</m-button>
		</div>
	</div>
	<buy-phone-dialog-comp ref="buyPhoneDialogRef" @showPrivilegePhoneDialog="showPrivilegePhoneDialog" />
	<privilege-phone-dialog-comp ref="privilegePhoneDialogRef" @handleBuy="handleBuy" />
</template>

<script lang="ts" src="./index.ts"></script>
<style lang="less" src="./index.less" scoped></style>
