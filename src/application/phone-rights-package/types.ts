import { PhoneLeadStatusEnum } from '@/application/phone-rights-package/constants';
import { ShowInfoResponse } from '@/application/goods-center/comps/the-stat/types';

export interface GoodsInfoResponse {
	itemList: ItemListModel[];
}

export interface ItemListModel {
	/** 商品code */
	goodsCode: string;
	/** 线索包商品类型,可用值:UNKNOWN,SILVER_VIP,GOLD_VIP,DIAMOND_VIP,BLACK_GOLD_VIP,SEARCH_AD,ENTER_ADVERT,RANK_ADVERT,BIG_CAR_LEAD_PACKAGE,MOTOR_LEAD_PACKAGE,PHONE_LEAD_PACKAGE,OFFLINE_SERVICE,COIN,SMS_PACK,PAAS,PHONE_EQUITY_CARD */
	tradeGoodsEnum: string;
	/** 有效天数 */
	effectiveDay: number;
	/** 价格(元) */
	price: number;
	/** 商品名称 */
	goodsName: string;
}

export interface StateModel {
	phoneRightView: ShowInfoResponse;
	goodsInfo: ItemListModel;
	authIsShow: boolean;
	hideDirected: boolean;
}

// 电话线索服务的状态
export interface JiaxiaoPhoneLeadResponse {
	bindTime: number;
	complaintPhone: string;
	complaintPhoneMask: string;
	id: number;
	jiaxiaoId: number;
	lastRefusePhoneMask: string;
	limitModifyCount: number;
	modifiedCount: number;
	newPhone: string;
	newPhoneMask: string;
	phone: string;
	phoneMask: string;
	privatePhone: string;
	ratio: string;
	showCloseUI: boolean;
	showComplaintPhoneUI: boolean;
	showList: boolean;
	sparePhoneMask: string;
	status: PhoneLeadStatusEnum;
}
