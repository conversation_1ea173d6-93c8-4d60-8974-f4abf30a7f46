.phone-rights-package {
	.header-title {
		padding-left: 20px;
		height: 70px;
		line-height: 70px;
		font-size: 16px;
		font-weight: 500;
		color: #212121;
		border-bottom: 1px solid #e2e9f3;
	}
	.content {
		padding: 20px;
		.content-info {
			background: rgba(30, 123, 249, 0.05);
			border-radius: 4px;
			padding: 20px 0 0 20px;
			display: flex;
			.left {
				width: 60%;
				.left-top {
					.title {
						font-size: 20px;
						font-weight: 500;
						margin-bottom: 20px;
						span {
							font-size: 28px;
							color: var(--ant-primary-color);
						}
					}
					.description {
						line-height: 30px;
						color: rgba(0, 0, 0, 0.65);
					}
				}
				.left-bottom {
					margin-top: 80px;
					.title {
						font-weight: 500;
					}
					.description {
						color: rgba(0, 0, 0, 0.65);
						font-size: 12px;
					}
				}
			}
			.right {
				margin: 0 auto;
				width: 251px;
				height: 316px;
				background: url('./img/bg_phone.png') no-repeat center;
				background-size: 100%;
			}
		}
		.content-step {
			.title {
				margin-top: 30px;
				font-size: 20px;
				font-weight: 500;
			}
			.steps {
				margin-top: 15px;
				color: #39393a;
				font-weight: 500;
				.header {
					display: flex;
					justify-content: space-between;
					align-items: center;
					height: 40px;
				}
				img {
					width: 100%;
					height: auto;
				}
				// .step1 {
				// 	width: 100%;
				// 	height: 200px;
				// 	background: url('./img/step1.png') no-repeat center;
				// 	background-size: cover;
				// }
				// .step2 {
				// 	width: 490px;
				// 	height: 260px;
				// 	background: url('./img/step2.png') no-repeat center;
				// 	background-size: 100%;
				// }
				// .step3 {
				// 	width: 490px;
				// 	height: 260px;
				// 	background: url('./img/step3.png') no-repeat center;
				// 	background-size: 100%;
				// }
				// .step4 {
				// 	width: 490px;
				// 	height: 260px;
				// 	background: url('./img/step4.png') no-repeat center;
				// 	background-size: 100%;
				// }
			}
		}
	}
	.pay-button {
		width: 100%;
		height: 70px;
		background-color: #fff;
		position: sticky;
		bottom: 0;
		text-align: center;
		border-top: 1px solid var(--border-color-light);
		border-radius: 0 0 4px 4px;

		.ant-btn {
			font-size: 16px;
			font-weight: 400;
			color: #ffffff;
			line-height: 22px;
			padding: 9px 68px;
			margin: 15px auto;
			height: auto;
		}
	}
}
