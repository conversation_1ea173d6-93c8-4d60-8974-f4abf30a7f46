import { defineComponent, onMounted, reactive, ref, toRefs } from 'vue';
import { MESSAGE_TYPE, MUtils } from '@paas/paas-library';
import { PaasPostMessage } from '@paas/paas-library';
import { useAccountInfoStore } from '@/pinia/account-info';
import { useOpenPayAccountAuthStore } from '@/pinia/open-pay-account-auth';
import { GetGoodsInfoStore } from '@/application/phone-rights-package/store';
import { GetShowInfoStore } from '@/application/goods-center/comps/the-stat/store';
import { GetJiaxiaoHideDirectedStore } from '@/utils/store';
import BuyPhoneDialogComp from '@/application/phone-rights-package/comps/buy-phone-dialog/index.vue';
import PrivilegePhoneDialogComp from '@/application/phone-rights-package/comps/privilege-phone-dialog/index.vue';
import { StateModel } from '@/application/phone-rights-package/types';
import { JiaxiaoTypeEnum, SubTypeEnum } from '@/utils/constant';

export default defineComponent({
	components: {
		BuyPhoneDialogComp,
		PrivilegePhoneDialogComp
	},
	setup() {
		const accountInfoStore = useAccountInfoStore();
		const openPayAccountAuthStore = useOpenPayAccountAuthStore();

		const state = reactive<StateModel>({
			phoneRightView: null,
			goodsInfo: null,
			authIsShow: false,
			hideDirected: false
		});
		const constants = {};

		const components = {
			buyPhoneDialogRef: ref<typeof BuyPhoneDialogComp>(null),
			privilegePhoneDialogRef: ref<typeof PrivilegePhoneDialogComp>(null)
		};
		const methods = {
			init() {
				methods.getHideDirected();
				methods.getPhoneRightView();
				methods.getGoodsInfo();
			},
			getHideDirected() {
				GetJiaxiaoHideDirectedStore.request()
					.getData()
					.then(res => {
						state.hideDirected = res.value;
					});
			},
			// 畅享电话卡展示
			getPhoneRightView() {
				GetShowInfoStore.request()
					.getData()
					.then(res => {
						console.log(res);
						state.phoneRightView = res;
						if (!res.recruitCertification && !res.qualificationExemptionExpire) {
							state.authIsShow = true;
						}
					});
			},
			// 获取商品信息
			getGoodsInfo() {
				GetGoodsInfoStore.request()
					.getData()
					.then(res => {
						console.log(res.itemList[0]);
						if (res.itemList.length > 0) {
							state.goodsInfo = res.itemList[0];
						}
					});
			},
			// 后续判断
			nextVerify() {
				if (
					state.phoneRightView.jxType !== JiaxiaoTypeEnum.MASTER &&
					state.phoneRightView.jxSubType !== SubTypeEnum.INDEPENDENT
				) {
					MUtils.toast('非独立主体驾校不能购买畅享电话卡', MESSAGE_TYPE.warning);
					return;
				}
				if (
					state.phoneRightView.recruitCertification ||
					(!state.phoneRightView.recruitCertification && state.phoneRightView.qualificationExemptionExpire)
				) {
					if (state.phoneRightView.vip) {
						MUtils.toast('购买会员驾校不能同时购买畅享电话卡', MESSAGE_TYPE.warning);
					} else {
						if (state.phoneRightView.recentAutoReceiveLead) {
							MUtils.toast('近期常规接收线索驾校不能购买畅享电话卡', MESSAGE_TYPE.warning);
						} else {
							methods.showBuyPhoneDialog();
						}
					}
				} else if (
					!state.phoneRightView.recruitCertification &&
					!state.phoneRightView.qualificationExemptionExpire
				) {
					MUtils.confirm({
						title: '温馨提示',
						content: '请先完成招生授权',
						type: MESSAGE_TYPE.warning,
						confirmText: '去完成'
					}).then(bool => {
						if (!bool) {
							return;
						}
						methods.jumpAuthPage();
					});
				}
			},
			// 购买
			async handleBuy(buyAgain) {
				await accountInfoStore.fetchAccountAndCoinInfo();
				if (accountInfoStore.isError) {
					return;
				}
				if (buyAgain === true) {
					methods.nextVerify();
					return;
				}
				if (state.phoneRightView.paid) {
					if (state.phoneRightView.latestPayNumber) {
						openPayAccountAuthStore.open({
							orderNumber: state.phoneRightView.latestPayNumber,
							hideDialog: true,
							beforeCallback: res => {
								if (res.err === true) {
									methods.nextVerify();
								} else if (res.isNeedAuth === true) {
									components.privilegePhoneDialogRef.value.show(
										state.phoneRightView.latestPayNumber,
										0
									);
								} else if (res.isNeedAuth === false) {
									if (state.phoneRightView.remainUseDays <= 30) {
										methods.nextVerify();
									} else if (state.phoneRightView.remainUseDays > 30) {
										MUtils.toast('畅享电话卡到期前30天才能续费', MESSAGE_TYPE.warning);
									}
								}
							}
						});
					} else {
						methods.nextVerify();
					}
				} else if (!state.phoneRightView.paid) {
					if (state.phoneRightView.latestPayNumber) {
						openPayAccountAuthStore.open({
							orderNumber: state.phoneRightView.latestPayNumber,
							hideDialog: true,
							beforeCallback: res => {
								if (res.isNeedAuth === true) {
									components.privilegePhoneDialogRef.value.show(
										state.phoneRightView.latestPayNumber,
										0
									);
								} else if (res.err === true || res.isNeedAuth === false) {
									methods.nextVerify();
								}
							}
						});
					} else {
						methods.nextVerify();
					}
				}
			},
			// 打开购买弹框
			showBuyPhoneDialog() {
				components.buyPhoneDialogRef.value.show(state.goodsInfo);
			},
			// 打开授权弹框
			showPrivilegePhoneDialog(orderNumber, current) {
				components.privilegePhoneDialogRef.value.show(orderNumber, current);
			},
			// 跳转到认证页面
			jumpAuthPage() {
				PaasPostMessage.post('base://get.path-jump', {
					id: 'W50401',
					appName: 'saas'
				});
			}
		};

		onMounted(() => {
			methods.init();
		});

		return {
			...toRefs(state),
			...constants,
			...components,
			...methods
		};
	}
});
