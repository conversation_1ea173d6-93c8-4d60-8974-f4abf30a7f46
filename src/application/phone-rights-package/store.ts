import { GoodsInfoStore, PaySuccessStore } from '@/store/jiaxiao-vip/web';
import {
	ClosePhoneClueStore,
	OpenPhoneClueStore,
	DelPhoneStore,
	JiaxiaoPhoneLeadStore
} from '@/store/jiaxiao-lead-filter/web';
import { GoodsInfoResponse, JiaxiaoPhoneLeadResponse } from '@/application/phone-rights-package/types';

export const GetGoodsInfoStore = new GoodsInfoStore<GoodsInfoResponse>({});

export const GetPaySuccessStore = new PaySuccessStore<{ value: boolean }>({});

// 获取电话线索服务的状态
export const GetJiaxiaoPhoneLeadStore = new JiaxiaoPhoneLeadStore<JiaxiaoPhoneLeadResponse>({});

// 关闭电话线索服务
export const FetchClosePhoneClueStore = new ClosePhoneClueStore({});

// 开启电话线索服务
export const FetchOpenPhoneClueStore = new OpenPhoneClueStore({});

// 删除招生电话
export const FetchDelPhoneStore = new DelPhoneStore<{ value: boolean }>({});
