import { defineComponent } from 'vue';
import { ModelController } from '@paas/paas-library';
import { GetBankTransferListStore } from '@/application/firm-record/store';
import { getTableColumn } from '@/application/firm-record/config';

export default defineComponent({
	setup() {
		const controller = new ModelController({
			table: {
				store: GetBankTransferListStore
			}
		});

		const constants = {
			COLUMNS: getTableColumn()
		};

		return {
			controller,
			...constants
		};
	}
});
