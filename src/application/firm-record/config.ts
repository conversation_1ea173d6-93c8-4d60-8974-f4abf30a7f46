import { ColumnXtype, MoneyUnit, TableColumn, TableDateFormat } from '@paas/paas-library';

export function getTableColumn(): TableColumn[] {
	return [
		{
			title: '转账到账时间',
			width: 140,
			dataIndex: 'payTime',
			dateFormat: TableDateFormat.MINUTES
		},
		{
			title: '汇款户名',
			width: 120,
			dataIndex: 'paymentAccountName'
		},
		{
			title: '汇款银行',
			width: 140,
			dataIndex: 'paymentBankName'
		},
		{
			title: '汇款账户',
			width: 220,
			dataIndex: 'paymentAccount'
		},
		{
			title: '收款账户',
			width: 220,
			dataIndex: 'receivingAccount'
		},
		{
			title: '实际汇款金额',
			width: 140,
			xtype: ColumnXtype.MONEY,
			moneyConfig: {
				unit: MoneyUnit.YUAN
			},
			dataIndex: 'transferRealAmount'
		},
		{
			title: '状态',
			width: 120,
			dataIndex: 'statusDesc'
		}
	];
}
