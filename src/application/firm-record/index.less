.rights-detail {
	.title {
		font-size: 16px;
		line-height: 22px;
		padding: 20px;
		font-weight: bold;
	}

	.introduce {
		margin: 0 20px;
		padding: 20px 0;
		border-top: 1px solid var(--color-lighter);

		h3 {
			font-size: 16px;
			font-weight: 600;
			color: var(--text-primary);
			line-height: 22px;
			margin: 5px 0 0;
		}

		h4 {
			font-size: 14px;
			font-weight: 600;
			color: var(--text-primary);
			background-color: #f6f8fa;
			line-height: 20px;
			margin: 20px 0 0;
			padding: 9px 20px;
			position: relative;

			&:before {
				content: '';
				display: inline-block;
				width: 5px;
				height: 24px;
				background-color: var(--ant-primary-color);
				border-radius: 0 3px 3px 0;
				position: absolute;
				left: 0;
				top: 7px;
			}
		}

		.content {
			display: flex;
			justify-content: space-between;
			padding-top: 20px;

			.text {
				width: 0;
				flex: 1;
				margin-right: 20px;

				p {
					font-size: 14px;
					font-weight: 400;
					color: var(--text-primary);
					line-height: 23px;
					margin: 0 0 15px;

					&:last-child {
						margin-bottom: 0;
					}

					&.remark {
						color: var(--info-active);
					}

					span {
						color: var(--ant-primary-color) !important;
					}
				}
			}
		}
	}
}
