import { defineComponent } from 'vue';
import { ModelController } from '@paas/paas-library';
import { GetSaleExchangeListStore } from '@/application/distribution-for-gold-detail/store';
import { getTableColumn } from '@/application/distribution-for-gold-detail/config';
import { ColorEnum } from '@/components/text-color/constants';

export default defineComponent({
	setup() {
		const controller = new ModelController({
			table: {
				store: GetSaleExchangeListStore
			}
		});

		const constants = {
			COLUMNS: getTableColumn()
		};

		const methods = {
			getStatusInfo(status) {
				return {
					color:
						[ColorEnum.GREEN, ColorEnum.RED, ColorEnum.PINK, ColorEnum.ORANGE][status] || ColorEnum.PURPLE,
					dsc: ['正常', '禁用', '退出', '非正常'][status] || ''
				};
			}
		};

		return {
			controller,
			...constants,
			...methods
		};
	}
});
