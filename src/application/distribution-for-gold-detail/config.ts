import { ColumnXtype, TableColumn, TableDateFormat } from '@paas/paas-library';

export function getTableColumn(): TableColumn[] {
	return [
		{
			title: '参与活动时间',
			dataIndex: 'createTime',
			dateFormat: TableDateFormat.MINUTES
		},
		{
			title: '邀请用户姓名',
			dataIndex: 'name'
		},
		{
			title: '邀请用户手机号',
			dataIndex: 'phoneMask'
		},
		{
			title: '认证状态',
			dataIndex: 'certStatus',
			render(data) {
				return data ? '已认证' : '未认证';
			}
		},
		{
			title: '活动状态',
			dataIndex: 'status',
			xtype: ColumnXtype.CUSTOM,
			useConfigFixed: false
		},
		{
			title: '兑换元宝总额',
			dataIndex: 'exchangeNum',
			render(data) {
				return data + '元宝';
			}
		},
		{
			title: '更新时间',
			dataIndex: 'updateTime',
			dateFormat: TableDateFormat.MINUTES
		}
	];
}
