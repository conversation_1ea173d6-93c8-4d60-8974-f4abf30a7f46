<template>
	<pm-dialog
		v-model:visible="visible"
		title="预览发票"
		width="750px"
		centered
		@mounted="onDialogMounted"
		@close="onClose"
		:footer="null"
	>
		<div
			v-if="activeData"
			style="margin-bottom: 20px; display: flex; align-items: center; justify-content: space-between"
		>
			<div class="flex-a-center">
				<div class="mr-10">
					开票类型：
					<span>{{ activeData.issuingTypeDesc }}；</span>
				</div>

				<div>
					开票状态:
					<span class="success">已开票</span>
				</div>
			</div>

			<div class="down-a" @click="onDownload">
				<VerticalAlignBottomOutlined class="mr-5" />
				<a style="cursor: pointer">下载发票</a>
			</div>
		</div>

		<div class="pdf-container">
			<canvas ref="pdfCanvas"></canvas>
		</div>

		<div class="paging" v-if="showData.length > 1">
			<m-button @click="changeInvoice('prev')" :disabled="previewIndex === 0" type="link">上一张</m-button>

			<span>
				{{ previewIndex + 1 }}
				/
				{{ showData.length }}
			</span>

			<m-button @click="changeInvoice('next')" :disabled="previewIndex === showData.length - 1" type="link">
				下一张
			</m-button>
		</div>
	</pm-dialog>
</template>
<script lang="ts">
import { defineComponent, ref, reactive, toRefs, computed } from 'vue';

import { VerticalAlignBottomOutlined } from '@ant-design/icons-vue';

import * as pdfjsLib from 'pdfjs-dist/build/pdf.min.mjs';

import { InvoiceColorTypeEnum } from '../../constants';
import { InvoicePreviewResponse } from '../../types';

pdfjsLib.GlobalWorkerOptions.workerSrc = '../resources/pdf.worker.min.js';

export default defineComponent({
	name: 'InvoicePreview',
	components: {
		VerticalAlignBottomOutlined
	},
	setup(props, { emit }) {
		const pdfCanvas = ref<HTMLCanvasElement | null>(null);

		const state = reactive({
			visible: false,
			showData: [] as InvoicePreviewResponse[],
			previewIndex: 0
		});

		const constants = {
			InvoiceColorTypeEnum
		};

		const activeData = computed(() => {
			return state.showData[state.previewIndex] || null;
		});

		const methods = {
			show(data: InvoicePreviewResponse[]) {
				state.showData = data;

				state.visible = true;
			},

			onDownload() {
				methods.downPdfFormUrl(activeData.value);
			},

			async downPdfFormUrl(item: InvoicePreviewResponse) {
				const { invoicePreviewUrl, serialNo, issuingTypeDesc } = item;

				// 使用 pdfjs 获取 PDF 数据并下载
				const loadingTask = pdfjsLib.getDocument(invoicePreviewUrl);
				const pdf = await loadingTask.promise;

				// 获取 PDF 的原始数据
				const data = await pdf.getData();

				// 创建 Blob 对象
				const blob = new Blob([data], { type: 'application/pdf' });

				// 创建下载链接
				const blobUrl = window.URL.createObjectURL(blob);
				const link = document.createElement('a');
				link.href = blobUrl;
				link.download = `${issuingTypeDesc}_${serialNo}.pdf`;

				// 将链接添加到DOM并触发下载
				document.body.appendChild(link);
				link.click();
				document.body.removeChild(link);

				// 清理 blob URL 以释放内存
				window.URL.revokeObjectURL(blobUrl);
			},

			async renderPdf() {
				let url = activeData.value?.invoicePreviewUrl;

				if (!url || !pdfCanvas.value) {
					return;
				}

				const loadingTask = pdfjsLib.getDocument(url);

				const pdf = await loadingTask.promise;

				const page = await pdf.getPage(1);
				const viewport = page.getViewport({ scale: 1.1 });

				const canvas = pdfCanvas.value;

				const context = canvas.getContext('2d');
				canvas.height = viewport.height;
				canvas.width = viewport.width;

				context.clearRect(0, 0, canvas.width, canvas.height);

				await page.render({ canvasContext: context, viewport }).promise;
			},

			onDialogMounted() {
				methods.renderPdf();
			},

			changeInvoice(type: 'next' | 'prev') {
				if (type === 'next') {
					state.previewIndex++;
				} else {
					state.previewIndex--;
				}

				methods.renderPdf();
			},

			onClose() {
				state.visible = false;

				state.showData = [];

				state.previewIndex = 0;

				emit('close');
			}
		};

		return {
			pdfCanvas,
			activeData,
			...toRefs(state),
			...methods,
			...constants
		};
	}
});
</script>
<style lang="less" scoped>
.flex-a-center {
	align-items: center;
	display: flex;
}

.down-a {
	color: var(--ant-primary-color);
	cursor: pointer;
}

.pdf-container {
	max-height: 600px;
	min-height: 290px;
	background-color: #f5f5f5;
	padding: 18px;
	overflow: auto;
	display: flex;
	justify-content: center;
}

.paging {
	margin-top: 10px;
	display: flex;
	align-items: center;
	justify-content: center;
}
</style>
