import {
	HistoryOrderResponse,
	InvoiceBuyerInfoModel
} from '@/application/invoice-management/components/Invoice-application-dialog/types';
import { HistoryOrderListStore } from '@/store/jiaxiao-vip/invoice';
import { PaasListResponse } from '@paas/paas-library';
import { InvoiceBuyerInfoStore } from '@/store/jiaxiao-vip/invoice-buyer-info';

export const GetHistoryOrderListStore = new HistoryOrderListStore<PaasListResponse<HistoryOrderResponse>>({});

export const GetInvoiceBuyerInfoStore = new InvoiceBuyerInfoStore<InvoiceBuyerInfoModel>({});
