import { computed, defineComponent, reactive, ref, toRefs } from 'vue';
import { MESSAGE_TYPE, MUtils, PhoneRegExp } from '@paas/paas-library';
import { type FormInstance } from 'ant-design-vue';
import { ExclamationCircleFilled } from '@ant-design/icons-vue';
import {
	BANK_ACCOUNT_PATTERN,
	BANK_NAME_PATTERN,
	BuyerTypeEnum,
	EMAIL_PATTERN,
	HEADER_TYPE_OPTIONS,
	INVOICE_TYPE_OPTIONS,
	InvoiceTypeEnum,
	MAX_ADDRESS_LENGTH,
	PHONE_PATTERN
} from '../../constants';
import { FetchHistoryOrderInvoiceStore, GetApplyInvoicePreviewStore, SubmitInvoiceApplicationStore } from '../../store';
import type { ApplyInvoicePreviewResponse } from '../../types';
import { StateModel } from '@/application/invoice-management/components/Invoice-application-dialog/types';
import {
	GetHistoryOrderListStore,
	GetInvoiceBuyerInfoStore
} from '@/application/invoice-management/components/Invoice-application-dialog/store';
import { useUserProfileStore } from '@/pinia/user-profile';
import InvoiceOrderSelector from '@/application/invoice-management/components/invoice-order-selector/index.vue';
import { BuyerInfoStatusEnum } from '@/application/invoice-management/components/Invoice-application-dialog/constants';

export default defineComponent({
	name: 'InvoiceApplicationDialog',
	components: {
		ExclamationCircleFilled,
		InvoiceOrderSelector
	},
	emits: ['close', 'success'],
	setup(props, { emit }) {
		const userProfileStore = useUserProfileStore();

		const formRef = ref<FormInstance>();
		const orderSelectorRef = ref<InstanceType<typeof InvoiceOrderSelector>>();

		const state = reactive<StateModel>({
			visible: false,
			submitLoading: false,
			// 订单号列表
			orderNos: [],
			// 是否是2021年1月1日之前购买的老订单
			isHistoryOrder: false,
			// 历史订单列表
			historyOrderOptions: [],
			// 订单金额列表
			orderAmounts: [],
			// 是否有预填信息
			hasBuyerInfo: false,
			buyerInfo: null,
			// 选中的历史订单
			orderSelectList: [],
			// 表单数据
			formData: {
				invoiceAmount: 0,
				buyerType: BuyerTypeEnum.COMPANY,
				invoiceType: InvoiceTypeEnum.ELECTRONIC_COMMON,
				buyerName: '',
				buyerIdNum: '',
				receiveEmail: '',
				buyerBankName: '',
				buyerBankAccount: '',
				buyerAddress: '',
				buyerMobileNum: ''
			}
		});

		// 计算属性
		const computeds = {
			// 是否为专用发票
			isSpecialInvoice: computed(() => {
				return state.formData.invoiceType === InvoiceTypeEnum.ELECTRONIC_SPECIAL;
			}),

			// 可用的发票类型选项
			availableInvoiceTypes: computed(() => {
				// 若抬头类型为个人，只能选择普通发票
				if (state.formData.buyerType === BuyerTypeEnum.PERSONAL) {
					return INVOICE_TYPE_OPTIONS.filter(option => option.value === InvoiceTypeEnum.ELECTRONIC_COMMON);
				}
				// 企业类型可以选择所有发票类型
				return INVOICE_TYPE_OPTIONS;
			}),

			// 是否可以编辑开票金额（老订单且只有一条数据时可编辑）
			canEditInvoiceAmount: computed(() => {
				return (
					(state.isHistoryOrder && state.orderSelectList?.length === 1) ||
					(!state.isHistoryOrder && state.orderNos.length <= 1)
				);
			}),

			// 最大开票金额（当前选中订单的可开票金额）
			maxInvoiceAmount: computed(() => {
				return state.orderAmounts[0].invoiceAmount;
			}),

			// 发票的额外验证规则
			specialInvoiceRules: computed(() => {
				return {
					buyerBankName: [
						{
							required: computeds.isSpecialInvoice.value,
							trigger: ['change', 'blur'],
							validator(rule: any, value: string) {
								if (!value && computeds.isSpecialInvoice.value) {
									return Promise.reject('请输入开户银行');
								}
								if (value && !BANK_NAME_PATTERN.test(value)) {
									return Promise.reject('开户银行只能包含中文、英文和常用符号');
								}
								return Promise.resolve();
							}
						}
					],
					buyerBankAccount: [
						{
							required: computeds.isSpecialInvoice.value,
							trigger: ['change', 'blur'],
							validator(rule: any, value: string) {
								if (!value && computeds.isSpecialInvoice.value) {
									return Promise.reject('请输入银行账号');
								}
								if (value && !BANK_ACCOUNT_PATTERN.test(value)) {
									return Promise.reject('银行账号只能包含英文和数字');
								}
								return Promise.resolve();
							}
						}
					],
					buyerAddress: [
						{
							required: computeds.isSpecialInvoice.value,
							validator(rule: any, value: string) {
								if (!value && computeds.isSpecialInvoice.value) {
									return Promise.reject('请输入企业地址');
								}
								return Promise.resolve();
							}
						},
						{ max: MAX_ADDRESS_LENGTH, message: `企业地址不能超过${MAX_ADDRESS_LENGTH}个字符` }
					],
					buyerMobileNum: [
						{
							required: computeds.isSpecialInvoice.value,
							trigger: ['change', 'blur'],
							validator(rule: any, value: string) {
								console.log(value, computeds.isSpecialInvoice.value);
								if (!value) {
									if (computeds.isSpecialInvoice.value) {
										return Promise.reject('请输入企业电话');
									} else {
										return Promise.resolve();
									}
								}

								if (!PHONE_PATTERN.test(value)) {
									// 测试电话
									if (PhoneRegExp.test(value)) {
										return Promise.resolve();
									}

									return Promise.reject('请输入正确的电话号码格式（手机号11位或固定电话8位）');
								}

								return Promise.resolve();
							}
						}
					]
				};
			})
		};

		// 表单验证规则
		const formRules = {
			receiveEmail: [
				{ required: true, message: '请输入交付邮箱' },
				{ pattern: EMAIL_PATTERN, message: '请输入正确的邮箱格式' }
			],
			invoiceType: [{ required: true, message: '请选择发票类型' }],
			invoiceAmount: [
				{
					required: true,
					validator(rule: any, value: number) {
						if (!value || value <= 0) {
							return Promise.reject('请输入有效的开票金额');
						}
						if (computeds.canEditInvoiceAmount.value && value > computeds.maxInvoiceAmount.value) {
							return Promise.reject(
								`开票金额不能超过订单可开票金额：${computeds.maxInvoiceAmount.value}元`
							);
						}
						return Promise.resolve();
					}
				}
			]
		};

		const constants = {
			HEADER_TYPE_OPTIONS,
			INVOICE_TYPE_OPTIONS,
			MAX_ADDRESS_LENGTH
		};

		const methods = {
			// 显示弹窗
			async show(orderNos: string[]) {
				state.orderNos = orderNos;
				state.isHistoryOrder = false;

				const [invoiceBaseInfo] = await Promise.all([
					methods.getApplyInvoicePreview(orderNos),
					methods.getInvoiceBuyerInfo()
				]);

				// 设置表单初始值
				Object.assign(state.formData, {
					// 开票金额
					invoiceAmount: invoiceBaseInfo.invoiceAmount,
					// 默认选择企业抬头
					buyerType: BuyerTypeEnum.COMPANY,
					// 默认选择普通发票
					invoiceType: InvoiceTypeEnum.ELECTRONIC_COMMON
				});

				if (!state.hasBuyerInfo) {
					state.formData.buyerName = invoiceBaseInfo.buyerName;
					state.formData.buyerIdNum = invoiceBaseInfo.buyerIdNum;
				}

				state.visible = true;
			},

			// 老订单弹窗
			async showOldOrder() {
				state.orderNos = [];
				state.isHistoryOrder = true;

				// 设置表单初始值
				Object.assign(state.formData, {
					// 开票金额
					invoiceAmount: 0,
					// 默认选择企业抬头
					buyerType: BuyerTypeEnum.COMPANY,
					// 默认选择普通发票
					invoiceType: InvoiceTypeEnum.ELECTRONIC_COMMON
				});

				methods.getInvoiceBuyerInfo();
				methods.getOldOrderList();

				state.visible = true;
			},

			// 获取历史可开票订单
			getOldOrderList() {
				GetHistoryOrderListStore.request()
					.getData()
					.then(data => {
						console.log(data);
						state.historyOrderOptions = data.itemList;
					});
			},

			// 发票类型变化时的处理
			onInvoiceTypeChange() {
				// 切换到普通发票时，清空专用发票的必填字段验证错误
				if (state.formData.invoiceType === InvoiceTypeEnum.ELECTRONIC_COMMON) {
					formRef.value?.clearValidate([
						'buyerBankName',
						'buyerBankAccount',
						'buyerAddress',
						'buyerMobileNum'
					]);
				}
			},

			/**
			 * 订单选择变化处理
			 * @param orderSelectList 订单选择列表
			 */
			onOrderSelectChange(orderSelectList: string[]) {
				// 清空开票金额的表单校验
				formRef.value?.clearValidate(['invoiceAmount']);

				// 更新表单数据
				state.orderSelectList = orderSelectList;

				if (orderSelectList.length <= 0) {
					return;
				}

				if (state.isHistoryOrder) {
					state.submitLoading = true;

					methods
						.getApplyInvoicePreview(orderSelectList)
						.then(data => {
							state.formData.invoiceAmount = data.invoiceAmount;

							if (!state.hasBuyerInfo) {
								state.formData.buyerName = data.buyerName;
								state.formData.buyerIdNum = data.buyerIdNum;
							}
						})
						.catch(() => {
							if (!state.hasBuyerInfo) {
								state.formData.buyerName = '';
								state.formData.buyerIdNum = '';
							}
						})
						.finally(() => {
							state.submitLoading = false;
						});
				}
			},

			// 提交表单
			async onSubmit() {
				try {
					// 同时校验主表单和订单选择器表单
					const validationPromises: Promise<boolean>[] = [];

					// 主表单校验
					const mainFormValidation = formRef.value
						?.validate()
						.then(() => {
							console.log('主表单校验通过');
							return true;
						})
						.catch(error => {
							console.log('主表单校验失败:', error);
							return false;
						});
					validationPromises.push(mainFormValidation);

					// 老订单模式下的订单选择器校验
					if (state.isHistoryOrder && orderSelectorRef.value) {
						const orderSelectValidation = orderSelectorRef.value.validateAll();
						validationPromises.push(orderSelectValidation);
					}

					// 等待所有校验完成
					const validationResults = await Promise.all(validationPromises);
					const allValid = validationResults.every(result => result === true);

					console.log('校验结果:', validationResults, '全部通过:', allValid);

					if (!allValid) {
						console.log('表单校验失败，停止提交');
						return;
					}

					// 老订单模式下的额外验证
					if (state.isHistoryOrder) {
						// 发票信息验证
						if (!state.formData.buyerName || !state.formData.buyerIdNum) {
							MUtils.toast('当前订单未查询到发票信息，无法开票，请联系客服处理', MESSAGE_TYPE.warning);
							return;
						}
					}

					state.submitLoading = true;

					const fn = state.isHistoryOrder ? FetchHistoryOrderInvoiceStore : SubmitInvoiceApplicationStore;
					console.log(fn);

					if (state.orderAmounts.length === 1) {
						state.orderAmounts[0].invoiceAmount = state.formData.invoiceAmount;
					}

					const params = MUtils.unDataProxy({
						...state.formData,
						orderAmounts: state.orderAmounts
					});

					console.log('订单开票申请数据:', params);

					const response = await fn.request(params).getData();
					console.log(response);

					if (response?.serialNo) {
						methods.onClose();
						emit('success');
						MUtils.confirm({
							title: '提交反馈',
							content: '本次开票申请已提交成功，请耐心等待审核！',
							confirmText: '我知道了'
						});
					}
				} catch (error) {
					console.error('开票申请提交失败:', error);
				} finally {
					state.submitLoading = false;
				}
			},

			// 关闭弹窗
			onClose() {
				state.visible = false;
				state.submitLoading = false;

				// 订单号列表
				state.orderNos = [];
				// 是否是2021年1月1日之前购买的老订单
				state.isHistoryOrder = false;
				// 历史订单列表
				state.historyOrderOptions = [];
				// 订单金额列表
				state.orderAmounts = [];
				// 有预填信息
				state.hasBuyerInfo = false;
				state.orderSelectList = [];

				// 重置表单
        Object.assign(state.formData, {
          invoiceAmount: 0,
          buyerType: BuyerTypeEnum.COMPANY,
          invoiceType: InvoiceTypeEnum.ELECTRONIC_COMMON,
          buyerName: '',
          buyerIdNum: '',
          receiveEmail: '',
          buyerBankName: '',
          buyerBankAccount: '',
          buyerAddress: '',
          buyerMobileNum: ''
        });
				// formRef.value?.resetFields();

				// 重置订单选择器
				orderSelectorRef.value?.reset();

				emit('close');
			},

			getApplyInvoicePreview(orderNos: string[]): Promise<ApplyInvoicePreviewResponse> {
				return new Promise((resolve, reject) => {
					orderNos = orderNos.filter(item => item);
					if (!orderNos?.length) {
						state.orderAmounts = [];
						reject();
						return;
					}

					GetApplyInvoicePreviewStore.request({
						jxId: userProfileStore.data.jiaxiaoId,
						orderNos,
						newOrder: !state.isHistoryOrder
					})
						.getData()
						.then(data => {
							state.orderAmounts = data.orderAmounts;
							resolve(data);
						})
						.catch(() => {
							state.orderAmounts = [];
							reject();
						});
				});
			},

			getInvoiceBuyerInfo(): Promise<void> {
				return new Promise(resolve => {
					GetInvoiceBuyerInfoStore.request()
						.getData()
						.then(data => {
							if (data?.status === BuyerInfoStatusEnum.ENABLE) {
								state.formData.buyerName = data.buyerName;
								state.formData.buyerIdNum = data.buyerIdNum;
								state.formData.receiveEmail = data.receiveEmail;
								state.formData.buyerBankName = data.buyerBankName;
								state.formData.buyerBankAccount = data.buyerBankAccount;
								state.formData.buyerAddress = data.buyerAddress;
								state.hasBuyerInfo = true;
							} else {
								state.hasBuyerInfo = false;
							}
							state.buyerInfo = data;

							resolve();
						})
						.catch(() => {
							state.hasBuyerInfo = false;
							state.buyerInfo = null;
							resolve();
						});
				});
			}
		};

		return {
			formRef,
			orderSelectorRef,
			...toRefs(state),
			...computeds,
			...constants,
			...methods,
			formRules
		};
	}
});
