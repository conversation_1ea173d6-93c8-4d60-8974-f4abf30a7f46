import { InvoiceApplicationFormModel, OrderAmountModel } from '@/application/invoice-management/types';
import { BuyerInfoStatusEnum } from '@/application/invoice-management/components/Invoice-application-dialog/constants';

export interface StateModel {
	visible: boolean;
	submitLoading: boolean;
	// 订单号列表
	orderNos: string[];
	// 是否是老订单
	isHistoryOrder: boolean;
	// 历史订单列表
	historyOrderOptions: HistoryOrderResponse[];
	// 订单金额列表
	orderAmounts: OrderAmountModel[];
	// 有预填信息
	hasBuyerInfo: boolean;
	buyerInfo: InvoiceBuyerInfoModel;
	// 选中的历史订单
	orderSelectList: string[];
	// 表单数据
	formData: InvoiceApplicationFormModel;
}

export interface HistoryOrderResponse {
	/**
	 * 账户明细Id
	 */
	id: number;

	/**
	 * 订单号
	 */
	orderNo: string;

	/**
	 * 消费时间
	 */
	actionTime: string;

	/**
	 * 商品名称
	 */
	goodsName: string;

	/**
	 * 备注
	 */
	comment: string;

	/**
	 * 订单金额
	 */
	orderAmount: number;

	/**
	 * 可开票金额
	 */
	invoiceableAmount: number;

	/**
	 * 能否申请开蓝票
	 */
	enableApplyBlueInvoice: boolean;

	createTime: number;
	paidTime: number;
}

export interface InvoiceBuyerInfoModel {
	buyerAddress: string;
	buyerBankAccount: string;
	buyerBankName: string;
	buyerIdNum: string;
	buyerName: string;
	createTime: number;
	createUserName: string;
	id: number;
	jiaxiaoId: number;
	jiaxiaoName: string;
	receiveEmail: string;
	status: BuyerInfoStatusEnum;
	updateTime: number;
	updateUserName: string;
}
