<template>
	<div class="app-container invoice-management">
		<pm-effi :controller="controller">
			<pm-search>
				<!-- 订单时间范围 -->
				<pm-search-single
					:span="8"
					:antdProps="{ placeholder: ['开始时间', '结束时间'], allowClear: true }"
					data-index="transactionTimeGte|transactionTimeLt"
					xtype="RANGEPICKER"
				/>
				<!-- 发票状态 -->
				<pm-search-single
					:span="4"
					:antdProps="{ placeholder: '发票状态', allowClear: true }"
					data-index="invoiceStatus"
					xtype="SELECT"
				/>
			</pm-search>

			<pm-table
				:columns="InvoiceTableColumns"
				:useCustomColumn="false"
				:helpIcon="false"
				:operations="false"
				:sort-num-fixed="true"
				:antdProps="{
					rowKey: 'orderNo',
					rowSelection: {
						preserveSelectedRowKeys: true,
						selectedRowKeys: selectedRowKeys,
						onChange: onSelectChange,
						fixed: true,
						getCheckboxProps: getCheckboxProps
					}
				}"
			>
				<template #beforeButtons>
					<payment-agreement-comp />
				</template>

				<template #beforeCustom>
					<m-button @click="onClickOldOrder" class="mr-8">历史订单开票</m-button>
					<m-button @click="onClickMakeInvoice" type="primary">去开票</m-button>
				</template>

				<template #invoiceStatusDesc="{ record }">
					<span :class="[getClass(record)]">{{ record.invoiceStatusDesc }}</span>
					<a
						href="javascript:;"
						class="sign"
						v-if="
							agreementViewStore.data.value &&
							!agreementViewStore.data.value.signed &&
							record.enableApplyBlueInvoice
						"
						@click="onSign"
					>
						签署
					</a>
				</template>
			</pm-table>
		</pm-effi>

		<!-- 开票申请弹窗 -->
		<invoice-application-dialog-comp
			ref="invoiceApplicationDialogRef"
			@close="onInvoiceSubmitClose"
			@success="onInvoiceSubmitSuccess"
		/>

		<!-- 代付款协议弹窗 -->
		<payment-agreement-dialog-comp ref="paymentAgreementDialogRef" />
	</div>
</template>
<script lang="ts" src="./index.ts"></script>
<style lang="less" src="./index.less" scoped></style>
