.invoice-order-selector {
	.order-select-item {
		margin-bottom: 16px;
		width: 100%;
		height: 32px;

		.order-select-row {
			width: 100%;
			display: flex;
			align-items: flex-start;

			.order-select-wrapper {
				width: 0;
				flex: 1;

				.order-select-form-item {
					margin-bottom: 0;
				}

				.order-select {
					width: 100%;
				}
			}

			.action-buttons {
				display: flex;
				align-items: center;
				justify-content: center;
				height: 32px;
				width: 32px;
				background: #ffffff;
				border: 1px solid rgba(0, 0, 0, 0.15);
				border-radius: 2px;
				margin-left: 8px;
				cursor: pointer;

				&:hover {
					border-color: var(--ant-error-color);

					.remove {
						background-color: var(--ant-error-color);
					}
				}

				.remove {
					width: 12px;
					height: 1.2px;
					background-color: #333;
				}
			}
		}

		&:last-child {
			margin-bottom: 0;
		}
	}

	.add-button-wrapper {
		margin-top: 16px;

		.add-btn {
			width: 100%;
			height: 32px;
			border: 1px solid rgba(0, 0, 0, 0.15);
			color: #666;
			display: flex;
			align-items: center;
			justify-content: center;

			&:hover {
				border-color: #1890ff;
				color: #1890ff;
				background-color: #f0f8ff;
			}

			&:focus {
				border-color: #1890ff;
				color: #1890ff;
			}
		}
	}
}
