import { defineComponent, reactive, ref, toRefs } from 'vue';
import { ModelController } from '@paas/paas-library';
import { InvoiceStatusOptionsStore, GetInvoiceRecordListStore, GetInvoicePreviewStore } from '../../store';
import { InvoiceRecordColumns } from '../../config';
import InvoicePreviewComp from '../Invoice-preview/index.vue';
import { InvoiceRecordStatusEnum } from '../../constants';
import { timeFilter } from '@/utils/utils';

export default defineComponent({
	name: 'InvoiceManagement',
	components: {
		InvoicePreviewComp
	},
	setup() {
		const state = reactive({
			selectedRowKeys: [] as string[],
			tableData: []
		});

		const controller = new ModelController({
			table: {
				store: GetInvoiceRecordListStore
			},
			search: {
				invoiceStatus: {
					store: InvoiceStatusOptionsStore
				}
			}
		});

		const constants = { InvoiceRecordColumns, InvoiceRecordStatusEnum };

		const components = {
			invoicePreviewRef: ref<InstanceType<typeof InvoicePreviewComp> | null>(null)
		};

		controller.table.onRequest.use(params => {
			return {
				...params
				// targetType: 3
			};
		});

		const methods = {
			onSelectChange(selectedRowKeys: string[]) {
				state.selectedRowKeys = selectedRowKeys;
			},

			async handleOpenInvoice(record) {
				try {
					const data = await GetInvoicePreviewStore.request({
						serialNo: record.serialNo
					}).getData();

					if (data && data.itemList.length) {
						components.invoicePreviewRef.value.show(data.itemList);
					}
				} catch (error) {
					console.error(error);
				}
			},

			async handleDownLoad(record) {
				const data = await GetInvoicePreviewStore.request({
					serialNo: record.serialNo
				}).getData();

				if (data && data.itemList.length) {
					data.itemList.reduce(
						(acc, item) => acc.then(() => components.invoicePreviewRef.value.downPdfFormUrl(item)),
						Promise.resolve()
					);
				}
			},

			timeFilter
		};

		return {
			...toRefs(state),
			...methods,
			...constants,
			...components,
			controller
		};
	}
});
