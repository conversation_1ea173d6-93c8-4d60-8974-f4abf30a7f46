import { Store } from '@simplex/simple-store';
import { PaasListResponse } from '@paas/paas-library';
import { INVOICE_STATUS_OPTIONS } from './constants';
import {
	InvoiceableOrdersListStore,
	InvoiceRecordListStore,
	InvoiceApplicationStore,
	ApplyInvoicePreviewStore,
	InvoicePreviewStore,
	HistoryOrderInvoiceStore
} from '@/store/jiaxiao-vip/invoice';
import type {
	ConsumptionRecordModel,
	InvoiceRecordResponse,
	ApplyInvoicePreviewResponse,
	InvoicePreviewResponse
} from './types';

/** 发票状态选项 */
export const InvoiceStatusOptionsStore = new Store(INVOICE_STATUS_OPTIONS);

/** 获取可开票订单列表 */
export const GetInvoiceableOrdersListStore = new InvoiceableOrdersListStore<PaasListResponse<ConsumptionRecordModel>>(
	{}
);

/** 获取发票记录列表 */
export const GetInvoiceRecordListStore = new InvoiceRecordListStore<PaasListResponse<InvoiceRecordResponse>>({});

/** 提交开票申请 */
export const SubmitInvoiceApplicationStore = new InvoiceApplicationStore<{ serialNo: string }>({});

// 历史订单申请开蓝票
export const FetchHistoryOrderInvoiceStore = new HistoryOrderInvoiceStore<{ serialNo: string }>({});

/** 获取驾校申请开票信息 */
export const GetApplyInvoicePreviewStore = new ApplyInvoicePreviewStore<ApplyInvoicePreviewResponse>({});

/** 预览发票 */
export const GetInvoicePreviewStore = new InvoicePreviewStore<PaasListResponse<InvoicePreviewResponse>>({});
