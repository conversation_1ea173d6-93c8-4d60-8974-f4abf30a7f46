/**
 * 发票管理页面配置
 */

import { ColumnXtype, MoneyUnit, TableColumn, TableDateFormat } from '@paas/paas-library';
import { INVOICE_RECORD_STATUS_OPTIONS } from './constants';

// 表格列配置
export const InvoiceTableColumns: TableColumn[] = [
	{
		title: '消费时间',
		dataIndex: 'transactionTime',
		width: 170,
		dateFormat: TableDateFormat.SECONDS
	},
	{
		title: '商品',
		dataIndex: 'goodsName',
		width: 150
	},
	{
		title: '交易金额',
		dataIndex: 'orderAmount',
		xtype: ColumnXtype.MONEY,
		moneyConfig: {
			unit: MoneyUnit.YUAN
		}
	},
	{
		title: '退单金额',
		dataIndex: 'refundAmount',
		xtype: ColumnXtype.MONEY,
		moneyConfig: {
			unit: MoneyUnit.YUAN
		}
	},
	{
		title: '可开票金额',
		dataIndex: 'invoiceableAmount',
		xtype: ColumnXtype.MONEY,
		moneyConfig: {
			unit: MoneyUnit.YUAN
		}
	},
	{
		title: '开票金额',
		dataIndex: 'blueInvoicedAmount',
		xtype: ColumnXtype.MONEY,
		moneyConfig: {
			unit: MoneyUnit.YUAN
		}
	},
	{
		title: '红冲金额',
		dataIndex: 'redInvoicedAmount',
		xtype: ColumnXtype.MONEY,
		moneyConfig: {
			unit: MoneyUnit.YUAN
		}
	},
	{
		title: '发票状态',
		dataIndex: 'invoiceStatusDesc',
		key: 'invoiceStatusDesc',
		xtype: ColumnXtype.CUSTOM,
		width: 140,
		minWidth: 140
	}
];

// 发票记录配置
export const InvoiceRecordColumns: TableColumn[] = [
	{
		title: '申请开票时间',
		dataIndex: 'applyTime',
		useConfigWidth: false,
		width: 180,
		dateFormat: TableDateFormat.SECONDS
	},
	{
		title: '详情',
		dataIndex: 'invoiceDetail',
		width: 300,
		xtype: ColumnXtype.CUSTOM
	},
	{
		title: '开票金额',
		dataIndex: 'invoiceAmount',
		xtype: ColumnXtype.MONEY,
		moneyConfig: {
			unit: MoneyUnit.YUAN
		}
	},
	{
		title: '红冲金额',
		dataIndex: 'redFlushAmount',
		xtype: ColumnXtype.MONEY,
		moneyConfig: {
			unit: MoneyUnit.YUAN
		}
	},
	{
		title: '开票类型',
		dataIndex: 'issuingTypes',
		width: 160,
		useConfigWidth: false,
		xtype: ColumnXtype.STRING,
		render(value) {
			return value.join(' + ');
		}
	},
	{
		title: '状态',
		dataIndex: 'invoiceShowStatus',
		minWidth: 150,
		xtype: ColumnXtype.STRING,
		customCell(data) {
			const value = data.invoiceStatus;

			const textClass = INVOICE_RECORD_STATUS_OPTIONS.find(option => option.key === value)?.textClass;

			if (textClass) {
				return { class: textClass };
			}
		}
	},
	{
		title: '开票成功时间',
		dataIndex: 'invoiceSuccessTime',
		width: 170,
		dateFormat: TableDateFormat.SECONDS
	}
];

// 默认筛选参数
export const DEFAULT_FILTER_PARAMS = {
	orderTimeRange: undefined,
	invoiceStatus: undefined
};

// 开票申请表单默认值
export const DEFAULT_INVOICE_FORM = {
	invoiceAmount: 0,
	headerType: undefined,
	invoiceType: undefined,
	buyerName: '',
	taxNumber: '',
	deliveryEmail: '',
	bankName: '',
	bankAccount: '',
	companyAddress: '',
	companyPhone: '',
	recordIds: []
};

// 红冲发票表单默认值
export const DEFAULT_RED_INVOICE_FORM = {
	invoiceCode: '',
	invoiceNumber: '',
	redAmount: 0,
	reason: '',
	originalInvoiceId: ''
};

// 配置文件，可以放置一些固定配置
export const SOME_CONFIG = 'some_value';
