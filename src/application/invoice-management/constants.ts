// 发票状态枚举
export enum InvoiceStatusEnum {
	/** 待开票 */
	PENDING = 10,
	/** 开票中 */
	PROCESSING = 20,
	/** 已开票 */
	COMPLETED = 30,
	/** 部分红冲 */
	PARTIAL_RED = 40,
	/** 全部红冲 */
	FULL_RED = 50
}

/** 开票记录状态枚举 */
export enum InvoiceRecordStatusEnum {
	/** 开票中 */
	INVOICING = 10,
	/** 开票成功 */
	INVOICE_SUCCESS = 20,
	/** 开票失败 */
	INVOICE_FAILURE = 30
}

export const INVOICE_RECORD_STATUS_OPTIONS = [
	{ value: '开票中', key: InvoiceRecordStatusEnum.INVOICING, textClass: 'warning' },
	{ value: '开票成功', key: InvoiceRecordStatusEnum.INVOICE_SUCCESS, textClass: 'success' },
	{ value: '开票失败', key: InvoiceRecordStatusEnum.INVOICE_FAILURE, textClass: 'danger' }
];

/** 发票状态选项 */
export const INVOICE_STATUS_OPTIONS = [
	{ value: '待开票', key: InvoiceStatusEnum.PENDING, textClass: 'primary' },
	{ value: '开票中', key: InvoiceStatusEnum.PROCESSING, textClass: 'warning' },
	{ value: '已开票', key: InvoiceStatusEnum.COMPLETED, textClass: 'success' },
	{ value: '部分红冲', key: InvoiceStatusEnum.PARTIAL_RED, textClass: 'danger' },
	{ value: '全部红冲', key: InvoiceStatusEnum.FULL_RED, textClass: 'danger' }
];

// 抬头类型枚举
export enum BuyerTypeEnum {
	/** 企业 */
	COMPANY = 20,
	/** 个人 */
	PERSONAL = 10
}

// 发票类型枚举
export enum InvoiceTypeEnum {
	/** 增值税电子普通发票 */
	ELECTRONIC_COMMON = 10,
	/** 增值税电子专用发票 */
	ELECTRONIC_SPECIAL = 20
}

// 发票类型选项
export const INVOICE_TYPE_OPTIONS = [
	{ label: '增值税电子普通发票', value: InvoiceTypeEnum.ELECTRONIC_COMMON },
	{ label: '增值税电子专用发票', value: InvoiceTypeEnum.ELECTRONIC_SPECIAL }
];

// 状态颜色映射
export const STATUS_COLOR_MAP = {
	[InvoiceStatusEnum.PENDING]: '#faad14',
	[InvoiceStatusEnum.PROCESSING]: '#1890ff',
	[InvoiceStatusEnum.COMPLETED]: '#52c41a',
	[InvoiceStatusEnum.PARTIAL_RED]: '#f5222d',
	[InvoiceStatusEnum.FULL_RED]: '#722ed1'
};

export enum ActiveTabKeyEnum {
	INVOICE_ABLE_LIST = 'invoice-able-list',
	INVOICE_RECORD = 'invoice-record'
}

// 抬头类型选项
export const HEADER_TYPE_OPTIONS = [
	{ label: '企业', value: BuyerTypeEnum.COMPANY },
	{ label: '个人', value: BuyerTypeEnum.PERSONAL }
];

export enum InvoiceColorTypeEnum {
	// 蓝色
	BLUE = 10,
	// 红色
	RED = 20
}

// 邮箱验证正则表达式
export const EMAIL_PATTERN = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

// 手机号验证正则表达式 (11位)
export const MOBILE_PATTERN = /^1[3-9]\d{9}$/;

// 固定电话验证正则表达式 (8位)
export const LANDLINE_PATTERN = /^\d{8}$/;

// 电话号码验证正则表达式 (包含手机号和固定电话)
export const PHONE_PATTERN = /^(1[3-9]\d{9}|\d{8})$/;

// 开户银行验证正则表达式 (中文+中英文符号)
export const BANK_NAME_PATTERN = /^[\u4e00-\u9fa5a-zA-Z\s\-()（）.·]+$/;

// 银行账号验证正则表达式 (纯数字或英文加数字组合，不允许纯英文)
export const BANK_ACCOUNT_PATTERN = /^(?=.*\d)[a-zA-Z0-9]+$|^\d+$/;

// 字符长度限制
export const MAX_ADDRESS_LENGTH = 100;
