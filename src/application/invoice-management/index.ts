import { defineComponent, reactive, toRefs } from 'vue';
import InvoiceAbleOrdersComp from './components/Invoice-able-orders/index.vue';
import InvoiceRecordComp from './components/Invoice-record/index.vue';
import { ActiveTabKeyEnum } from './constants';

export default defineComponent({
	name: 'InvoiceManagement',
	components: {
		InvoiceAbleOrdersComp,
		InvoiceRecordComp
	},
	setup() {
		const state = reactive({
			activeTabKey: ActiveTabKeyEnum.INVOICE_ABLE_LIST
		});

		const constants = {
			ActiveTabKeyEnum
		};

		const methods = {};

		return {
			...toRefs(state),
			...constants,
			...methods
		};
	}
});
