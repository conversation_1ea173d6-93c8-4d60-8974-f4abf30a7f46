import { ColumnXtype, TableColumn, TableDateFormat } from '@paas/paas-library';

export function getTableColumn(): TableColumn[] {
	return [
		{
			title: '时间',
			dataIndex: 'countDate',
			dateFormat: TableDateFormat.DAY
		},
		{
			title: '消费详情',
			dataIndex: 'typeDesc',
			xtype: ColumnXtype.CUSTOM
		},
		{
			title: '数量',
			dataIndex: 'count',
			xtype: ColumnXtype.CUSTOM
		},
		{
			title: '剩余可用余量',
			dataIndex: 'remainTotalCount',
			render: data => data + '条'
		}
	];
}
