import { defineComponent } from 'vue';
import { ModelController } from '@paas/paas-library';
import { GetDailyRecordStore } from '@/application/sms-rights-package-detail/store';
import { getTableColumn } from '@/application/sms-rights-package-detail/config';
import { TypeEnum } from '@/application/sms-rights-package-detail/constants';
import TextColorComp from '@/components/text-color/index.vue';
import { ColorEnum } from '@/components/text-color/constants';

export default defineComponent({
	components: {
		TextColorComp
	},
	setup() {
		const controller = new ModelController({
			table: {
				store: GetDailyRecordStore
			}
		});

		const constants = {
			COLUMNS: getTableColumn()
		};

		// 方法
		const methods = {
			getTypeRender(type) {
				const add = [TypeEnum.BUY, TypeEnum.FREE, TypeEnum.VIP_FREE].includes(type);
				return {
					color: add ? ColorEnum.GREEN : ColorEnum.RED,
					up: add ? '+' : '-'
				};
			}
		};

		return {
			...constants,
			...methods,
			controller
		};
	}
});
