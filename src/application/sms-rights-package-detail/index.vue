<template>
	<div class="app-container sms-rights-package-detail">
		<div class="title">短信权益包明细</div>

		<pm-effi :controller="controller">
			<pm-search>
				<pm-search-single
					:span="8"
					:antd-props="{ placeholder: ['消费开始时间', '消费结束时间'] }"
					data-index="startTime|endTime"
					xtype="RANGEPICKER"
				/>
			</pm-search>

			<pm-table
				:columns="COLUMNS"
				:useCustomColumn="true"
				:showCustom="true"
				:helpIcon="false"
				:operations="false"
			>
				<!-- 消费详情 -->
				<template #typeDesc="{ record }">
					<m-tag :color="getTypeRender(record.type).color">{{ record.typeDesc }}</m-tag>
				</template>

				<template #count="{ record }">
					<text-color-comp :color="getTypeRender(record.type).color">
						{{ getTypeRender(record.type).up + record.count + '条' }}
					</text-color-comp>
				</template>
			</pm-table>
		</pm-effi>
	</div>
</template>

<script lang="ts" src="./index.ts"></script>
<style lang="less" src="./index.less" scoped></style>
