import { defineComponent, reactive, toRefs } from 'vue';
import { StateModel } from './types';
import { copy2Clipboard } from '@/utils/utils';
import { MESSAGE_TYPE, MUtils } from '@paas/paas-library';

export default defineComponent({
	setup() {
		const state = reactive<StateModel>({
			visible: false,
			shareUrl: ''
		});

		// 方法
		const methods = {
			show(shareUrl: string) {
				state.visible = true;
				state.shareUrl = shareUrl;
			},
			// 复制
			onCopy() {
				copy2Clipboard(state.shareUrl, () => {
					MUtils.toast('复制成功', MESSAGE_TYPE.success);
				});
			},
			onClose() {
				state.visible = false;
			}
		};

		return {
			...toRefs(state),
			...methods
		};
	}
});
