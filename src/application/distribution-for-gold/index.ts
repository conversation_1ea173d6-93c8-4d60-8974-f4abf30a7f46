import { defineComponent, reactive, ref, toRefs } from 'vue';
import { MESSAGE_TYPE, MUtils } from '@paas/paas-library';
import { StateModel } from './types';
import { GetSaleExchangeShareUrlStore } from '@/application/distribution-for-gold/store';
import DistributionDialogComp from '@/application/distribution-for-gold/comps/distribution-dialog/index.vue';

export default defineComponent({
	components: {
		DistributionDialogComp
	},
	setup() {
		const state = reactive<StateModel>({
			loading: false
		});

		const components = {
			distributionDialogRef: ref<typeof DistributionDialogComp>(null)
		};

		// 方法
		const methods = {
			onSubmit() {
				state.loading = true;
				GetSaleExchangeShareUrlStore.request()
					.getData()
					.then(data => {
						const url = data.value;

						if (url) {
							components.distributionDialogRef.value.show(url);
						} else {
							MUtils.toast('未获取到分销信息，请稍后重试', MESSAGE_TYPE.error);
						}
					})
					.finally(() => {
						state.loading = false;
					});
			}
		};

		return {
			...toRefs(state),
			...components,
			...methods
		};
	}
});
