.pb-0 {
	padding-bottom: 0 !important;
}

.mb-0 {
	margin-bottom: 0 !important;
}

.sms-rights-package {
	.title {
		font-size: 24px;
		font-weight: 400;
		color: var(--text-primary);
		line-height: 44px;
		padding: 20px;
		margin: 0;
	}

	.introduce {
		padding: 0 20px;
		margin-bottom: 40px;
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		grid-column-gap: 15px;
		grid-row-gap: 15px;

		li {
			background-color: #f5f9ff;
			border-radius: 16px;
			padding: 20px 200px 47px 25px;
			position: relative;
			background-image: url('./img/mobile-1.png');
			background-position: right 36px bottom -145px;
			background-repeat: no-repeat;
			min-height: 138px;
			box-sizing: border-box;

			&:nth-child(4n - 2),
			&:nth-child(4n - 1) {
				background-color: #fffcf5;
			}

			&:nth-child(2) {
				background-image: url('./img/mobile-2.png');

				h5 {
					background-image: url('./img/emoji-2.png');
				}
			}

			&:nth-child(3) {
				background-image: url('./img/mobile-3.png');

				h5 {
					background-image: url('./img/emoji-3.png');
				}
			}

			&:nth-child(4) {
				background-image: url('./img/mobile-4.png');

				h5 {
					background-image: url('./img/emoji-4.png');
				}
			}

			h5 {
				font-size: 16px;
				font-weight: 600;
				text-align: left;
				color: var(--text-primary);
				line-height: 22px;
				padding-left: 26px;
				background: url('./img/emoji-1.png') left top no-repeat;
				background-size: 20px;
				margin: 0;
			}

			.remark {
				font-size: 12px;
				font-weight: 400;
				text-align: left;
				color: var(--text-primary);
				line-height: 22px;
				margin-top: 5px;
			}

			.tip {
				font-size: 12px;
				font-weight: 400;
				text-align: left;
				color: var(--color-warning);
				line-height: 22px;
				margin-top: 5px;
				background: url('./img/emoji-5.png') right center no-repeat;
				background-size: 16px 12px;
				display: inline-block;
				padding-right: 18px;
				position: absolute;
				left: 25px;
				bottom: 20px;
			}
		}
	}

	.title-second {
		font-size: 16px;
		font-weight: 500;
		color: var(--text-primary);
		line-height: 24px;
		padding: 0 20px;
		margin: 15px 0 0;
	}

	.list {
		margin-top: 15px;
		margin-bottom: 40px;
		padding: 0 20px;
		display: grid;
		grid-template-columns: repeat(4, 1fr);
		grid-column-gap: 15px;
		grid-row-gap: 15px;

		li {
			background-color: #ffffff;
			border: 1px solid #d9d9d9;
			border-radius: 4px;
			padding: 15px;
			font-size: 14px;
			font-weight: 400;
			color: var(--text-primary);
			line-height: 22px;
			cursor: pointer;
			position: relative;
			box-sizing: border-box;

			&.active {
				border-color: var(--ant-primary-6);

				&:after {
					content: '';
					display: inline-block;
					width: 32px;
					height: 32px;
					background: url('./img/active.png') center no-repeat;
					position: absolute;
					top: -1px;
					right: -1px;
					background-size: cover;
				}
			}
		}
	}

	.tips {
		font-size: 12px;
		font-weight: 400;
		color: var(--color-warning);
		line-height: 17px;
		margin: 10px 20px 40px;

		i {
			margin-right: 2px;
		}
	}

	.sms-number {
		li {
			text-align: center;

			&.active {
				.price {
					color: var(--ant-primary-6);
				}
			}

			.price {
				font-size: 14px;
				font-weight: 400;
				color: var(--text-primary);
				line-height: 44px;

				span {
					font-size: 30px;
					font-weight: 600;
				}
			}

			.number {
				font-size: 14px;
				font-weight: 500;
				color: var(--ant-primary-6);
				line-height: 22px;
				padding: 3px 16px;
				background: rgba(30, 123, 249, 0.05);
				border-radius: 15px;
				display: inline-block;
			}
		}
	}

	.button-imgs {
		margin: 15px 20px 80px;
		display: flex;

		li {
			width: 50%;

			&:first-child {
				margin-right: 14px;
			}

			img {
				width: 100%;
			}
		}
	}

	.pay-button {
		width: 100%;
		height: 70px;
		background-color: #fff;
		position: sticky;
		bottom: 0;
		text-align: center;
		border-top: 1px solid var(--border-color-light);
		border-radius: 0 0 4px 4px;

		.ant-btn {
			font-size: 16px;
			font-weight: 400;
			color: #ffffff;
			line-height: 22px;
			padding: 9px 68px;
			margin: 15px auto;
			height: auto;
		}
	}
}
