import { computed, defineComponent, onMounted, reactive, ref, toRefs } from 'vue';
import { StateModel } from './types';
import { GetMessageTemplateStore, GetSMSPriceStore } from '@/application/sms-rights-package/store';
import { MESSAGE_TYPE, MUtils } from '@paas/paas-library';
import { useAccountInfoStore } from '@/pinia/account-info';
import BuySmsDialogComp from '@/application/sms-rights-package/comps/buy-sms-dialog/index.vue';

export default defineComponent({
	components: {
		BuySmsDialogComp
	},
	setup() {
		const accountInfoStore = useAccountInfoStore();

		const state = reactive<StateModel>({
			// 短信模板列表
			SMSTemplate: [],
			// 短信价格列表
			SMSPrice: [],
			// 选中的短信条数
			SMSCount: null
		});

		const computeds = {
			currentSMSInfo: computed(() => {
				return state.SMSPrice?.find(item => item.count === state.SMSCount) || {};
			})
		};

		const components = {
			buySMSDialogRef: ref<typeof BuySmsDialogComp>(null)
		};

		// 方法
		const methods = {
			init() {
				methods.getSMSTemplate();
				methods.getSMSPrice();
			},
			// 选择短信包规格
			onChangeNumber(count) {
				state.SMSCount = count;
			},
			// 购买按钮
			async handleBuy() {
				MUtils.toast('当前服务已暂停，如需使用，请联系客服！', MESSAGE_TYPE.warning);
				// if (!state.SMSCount) {
				// 	MUtils.toast('请选择需要购买的短信条数', MESSAGE_TYPE.warning);
				// 	return;
				// }

				// await accountInfoStore.fetchAccountAndCoinInfo();
				// if (accountInfoStore.isError) {
				// 	return;
				// }

				// components.buySMSDialogRef.value.show(computeds.currentSMSInfo.value);
			},
			// 获取短信模板
			async getSMSTemplate() {
				state.SMSTemplate = await GetMessageTemplateStore.request().getData();
			},
			// 获取短信价格
			async getSMSPrice() {
				const SMSPrice = await GetSMSPriceStore.request().getData();
				state.SMSPrice = SMSPrice;

				let defaultPrice = SMSPrice.find(item => item.price === 110);
				defaultPrice = defaultPrice ? defaultPrice : SMSPrice[0];
				state.SMSCount = defaultPrice.count;
			}
		};

		onMounted(() => {
			methods.init();
		});

		return {
			accountInfoStore,
			...toRefs(state),
			...computeds,
			...components,
			...methods
		};
	}
});
