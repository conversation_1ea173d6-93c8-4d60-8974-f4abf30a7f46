export interface StateModel {
	// 短信模板列表
	SMSTemplate: SMSTemplateModel[];
	// 短信价格列表
	SMSPrice: SMSPriceModel[];
	// 选中的短信条数
	SMSCount: null;
}

export interface SMSTemplateModel {
	/** 短信模板code */
	code: string;
	/** 短信模板名称 */
	name: string;
	/** 短信模板内容 */
	content: string;
	/** 短信模板设置参数 */
	smsTempParams: string;
	/** 历史短信模板设置参数 */
	historySmsTempParams: string;
}

export interface SMSPriceModel {
	/** 短信条数 */
	count: number;
	/** 价格，单位元 */
	price: number;
}
