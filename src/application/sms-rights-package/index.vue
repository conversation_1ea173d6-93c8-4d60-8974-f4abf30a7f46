<template>
	<div class="sms-rights-package">
		<h3 class="title">短信权益包帮你分担招生难题</h3>
		<ul class="introduce">
			<li>
				<h5>电话无人接，接通直接挂</h5>
				<p class="remark">学员可能正在上课，没办法接听电话，事后看到陌生号码未接电话，也懒得回电</p>
				<p class="tip">想要有效联系学员，我们可以这么做</p>
			</li>
			<li>
				<h5>电话聊好好的，微信不通过</h5>
				<p class="remark">学员不知道加微信的人是谁，或者不想加陌生人微信</p>
				<p class="tip">提起告知学员你会加TA微信，避免唐突被拒</p>
			</li>
			<li>
				<h5>微信聊的挺好，就是迟迟不下单</h5>
				<p class="remark">学员可能在等着看看是不是还有更大的活动，可以享受更优惠的福利</p>
				<p class="tip">告知学员最大活动力度，一键促单</p>
			</li>
			<li>
				<h5>节假日活动，无法快速触达潜在学员</h5>
				<p class="remark">潜在学员群发，通知活动折扣，唤醒潜在用户</p>
				<p class="tip">直接触达潜在学员，二次召回学员促单</p>
			</li>
		</ul>
		<h3 class="title pb-0">短信权益包如何拥有？</h3>
		<h4 class="title-second">1.可用的短信模板</h4>
		<ul class="list sms-type">
			<li v-for="(item, index) in SMSTemplate" :key="item.code + index">{{ item.content }}</li>
		</ul>
		<h4 class="title-second">2.选择需要购买的短信条数</h4>
		<ul class="list sms-number">
			<li
				v-for="item in SMSPrice"
				:key="item.count"
				:class="{ active: SMSCount === item.count }"
				@click="onChangeNumber(item.count)"
			>
				<p class="price">
					<span>{{ item.price }}</span>
					元
				</p>
				<p class="number">{{ item.count }}条</p>
			</li>
		</ul>
		<h4 class="title-second">3.发送短信给用户</h4>
		<ul class="button-imgs">
			<li>
				<img src="./img/sms-1.png" alt="" />
			</li>
			<li>
				<img src="./img/sms-2.png" alt="" />
			</li>
		</ul>
		<div class="pay-button">
			<m-button type="primary" :loading="accountInfoStore.isLoading" @click="handleBuy">立即购买</m-button>
		</div>

		<buy-sms-dialog-comp ref="buySMSDialogRef" />
	</div>
</template>

<script lang="ts" src="./index.ts"></script>
<style lang="less" src="./index.less" scoped></style>
