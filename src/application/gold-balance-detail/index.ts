import ClueDetailComp from '@/application/gold-balance-detail/comps/clue-detail/index.vue';
import PayDetailComp from '@/application/gold-balance-detail/comps/pay-detail/index.vue';
import { TabsEnum } from '@/application/gold-balance-detail/constants';
import { defineComponent, onMounted, reactive, toRefs } from 'vue';
import { useRoute } from 'vue-router';
import { RouteQueryModel, StateModel } from './types';

export default defineComponent({
	components: {
		PayDetailComp,
		ClueDetailComp
	},
	setup() {
		const route = useRoute();
		const routeQuery: RouteQueryModel = route.query;

		const state = reactive<StateModel>({
			tabIndex: TabsEnum.PAY
		});

		const constants = {
			TabsEnum
		};

		// 方法
		const methods = {
			initTab() {
				const { tabName } = routeQuery;
				if (tabName === TabsEnum.CLUE) {
					state.tabIndex = TabsEnum.CLUE;
				}
			}
		};

		onMounted(() => {
			methods.initTab();
		});

		return {
			...toRefs(state),
			...constants,
			...methods
		};
	}
});
