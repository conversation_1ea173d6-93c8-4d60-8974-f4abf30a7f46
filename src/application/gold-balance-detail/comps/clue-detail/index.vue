<template>
	<pm-effi :controller="controller">
		<pm-search>
			<pm-search-single
				:span="8"
				:antd-props="{ placeholder: ['购买开始时间', '购买结束时间'] }"
				data-index="beginDate|endDate"
				xtype="RANGEPICKER"
			/>
		</pm-search>

		<pm-table :columns="COLUMNS" :useCustomColumn="true" :showCustom="true" :helpIcon="false" :operations="false">
			<template #beforeCustom>
				<confirm-button
					v-if="userProfileStore.isSuperAdmin"
					:loading="exportLoading"
					ghost
					type="primary"
					@click="onExport"
				>
					导出
				</confirm-button>
			</template>

			<!-- 购买详情 -->
			<template #licenses="{ record }">
				<template v-for="(item, index) in record.licenses" :key="index">
					<m-tag color="green">{{ `${item.license}: ${item.count}条` }}</m-tag>
				</template>
			</template>
		</pm-table>
	</pm-effi>
</template>
<script lang="ts" src="./index.ts"></script>
<style lang="less" src="./index.less" scoped></style>
