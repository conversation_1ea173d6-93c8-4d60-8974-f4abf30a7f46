import { ColumnXtype, TableColumn, TableDateFormat } from '@paas/paas-library';

export function getTableColumn(): TableColumn[] {
	return [
		{
			title: '购买时间',
			width: 140,
			dataIndex: 'date',
			dateFormat: TableDateFormat.DAY
		},
		{
			title: '购买详情',
			dataIndex: 'licenses',
			xtype: ColumnXtype.CUSTOM
		},
		{
			title: '购买条数',
			dataIndex: 'count',
			render: data => data + '条'
		},
		{
			title: '消耗元宝',
			dataIndex: 'spend',
			render: data => data + '元宝'
		}
	];
}
