import { PaasListResponse } from '@paas/paas-library';
import { ClueHistoryStatStore } from '@/store/volvo/h5';
import { HistoryStatResponse } from '@/application/gold-balance-detail/comps/clue-detail/types';

export const GetClueHistoryStatStore = new ClueHistoryStatStore<PaasListResponse<HistoryStatResponse>>({});

export const GetClueHistoryStatExportStore = new ClueHistoryStatStore<boolean>({
	method: 'ATAG_GET'
});
