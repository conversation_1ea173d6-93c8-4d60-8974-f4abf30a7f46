import { defineComponent, reactive, toRefs } from 'vue';
import { StateModel } from './types';
import { ModelController, MUtils } from '@paas/paas-library';
import {
	GetClueHistoryStatExportStore,
	GetClueHistoryStatStore
} from '@/application/gold-balance-detail/comps/clue-detail/store';
import { getTableColumn } from '@/application/gold-balance-detail/comps/clue-detail/config';
import { ConfirmButton } from '@paas/paas-base-lib';
import { useUserProfileStore } from '@/pinia/user-profile';

export default defineComponent({
	components: {
		ConfirmButton
	},
	setup() {
		const userProfileStore = useUserProfileStore();

		const state = reactive<StateModel>({
			exportLoading: false
		});

		const controller = new ModelController({
			table: {
				store: GetClueHistoryStatStore
			}
		});

		const constants = {
			COLUMNS: getTableColumn()
		};

		// 方法
		const methods = {
			onExport() {
				const params = {
					export: true,
					fileName: '元宝线索明细.xls',
					...MUtils.filterPagingParams(controller.table.getPrevParams())
				};

				state.exportLoading = true;
				GetClueHistoryStatExportStore.request(params)
					.getData()
					.finally(() => {
						setTimeout(() => {
							state.exportLoading = false;
						}, 5000);
					});
			}
		};

		return {
			userProfileStore,
			...toRefs(state),
			...constants,
			...methods,
			controller
		};
	}
});
