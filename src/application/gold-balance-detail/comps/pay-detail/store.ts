import { CoinDetailListStore } from '@/store/jiaxiao-vip/web';
import { PaasListResponse } from '@paas/paas-library';
import { CoinDetailResponse } from '@/application/gold-balance-detail/comps/pay-detail/types';

export const GetCoinDetailListStore = new CoinDetailListStore<PaasListResponse<CoinDetailResponse>>({});

export const GetCoinDetailListExportStore = new CoinDetailListStore<boolean>({
	method: 'ATAG_GET'
});
