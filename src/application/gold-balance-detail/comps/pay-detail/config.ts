import { TableColumn, TableDateFormat } from '@paas/paas-library';

export function getTableColumn(): TableColumn[] {
	return [
		{
			title: '时间',
			width: 140,
			dataIndex: 'createTime',
			dateFormat: TableDateFormat.MINUTES
		},
		{
			title: '交易类型',
			dataIndex: 'typeName'
		},
		{
			title: '交易详情',
			dataIndex: 'description'
		},
		{
			title: '交易元宝',
			dataIndex: 'payCoin'
		},
		{
			title: '元宝余额',
			dataIndex: 'coinBalance'
		},
		{
			title: '操作人员',
			dataIndex: 'createUserName'
		}
	];
}
