import { defineComponent, reactive, toRefs } from 'vue';
import { StateModel } from './types';
import { ModelController, MUtils } from '@paas/paas-library';
import { ConfirmButton } from '@paas/paas-base-lib';
import {
	GetCoinDetailListExportStore,
	GetCoinDetailListStore
} from '@/application/gold-balance-detail/comps/pay-detail/store';
import { getTableColumn } from '@/application/gold-balance-detail/comps/pay-detail/config';
import { useUserProfileStore } from '@/pinia/user-profile';

export default defineComponent({
	components: {
		ConfirmButton
	},
	setup() {
		const userProfileStore = useUserProfileStore();

		const state = reactive<StateModel>({
			exportLoading: false
		});

		const controller = new ModelController({
			table: {
				store: GetCoinDetailListStore
			}
		});

		const constants = {
			COLUMNS: getTableColumn()
		};

		// 方法
		const methods = {
			onExport() {
				const params = {
					export: true,
					fileName: '元宝消费明细.xls',
					...MUtils.filterPagingParams(controller.table.getPrevParams())
				};

				state.exportLoading = true;
				GetCoinDetailListExportStore.request(params)
					.getData()
					.finally(() => {
						setTimeout(() => {
							state.exportLoading = false;
						}, 5000);
					});
			}
		};

		return {
			userProfileStore,
			...toRefs(state),
			...constants,
			...methods,
			controller
		};
	}
});
