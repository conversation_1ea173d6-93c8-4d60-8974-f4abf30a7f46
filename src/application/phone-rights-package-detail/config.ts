import { TableColumn, TableDateFormat, ColumnXtype } from '@paas/paas-library';

export function getTableColumn(): TableColumn[] {
	return [
		{
			title: '消费时间',
			dataIndex: 'paidTime',
			dateFormat: TableDateFormat.SECONDS
		},
		{
			title: '交易金额',
			dataIndex: 'goodsPaid'
		},
		{
			title: '权益有效期',
			dataIndex: 'expiration',
			xtype: ColumnXtype.CUSTOM
		},
		{
			title: '状态',
			dataIndex: 'statusStr'
		}
	];
}
