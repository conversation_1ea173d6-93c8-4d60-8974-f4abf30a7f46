import { defineComponent } from 'vue';
import { ModelController } from '@paas/paas-library';
import { timeFilter } from '@/utils/utils';
import { GetOrderDetailStore } from '@/application/phone-rights-package-detail/store';
import { getTableColumn } from '@/application/phone-rights-package-detail/config';

export default defineComponent({
	setup() {
		const controller = new ModelController({
			table: {
				store: GetOrderDetailStore
			}
		});

		const constants = {
			COLUMNS: getTableColumn()
		};

		// 方法
		const methods = {};

		return {
			...constants,
			...methods,
			timeFilter,
			controller
		};
	}
});
