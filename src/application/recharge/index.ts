import { RouteQueryModel } from '@/application/recharge/types';
import RechargeDialogComp from '@/components/recharge-dialog/index.vue';
import { OpenModel, RechargeCallbackDataModel } from '@/components/recharge-dialog/types';
import { PaasPostMessage } from '@paas/paas-library';
import { defineComponent, onMounted, reactive, ref, toRefs } from 'vue';
import { useRoute } from 'vue-router';

export default defineComponent({
	components: {
		RechargeDialogComp
	},
	setup() {
		const route = useRoute();
		const routeQuery: RouteQueryModel = route.query;

		const state = reactive({
			loading: false
		});

		const components = {
			rechargeDialogRef: ref<typeof RechargeDialogComp>(null)
		};

		const methods = {
			open() {
				const { amount } = routeQuery;

				state.loading = true;
				components.rechargeDialogRef.value.checkShow({
					amount,
					beforeCallback: () => {
						state.loading = false;
					},
					callback: (data: RechargeCallbackDataModel) => {
						if (data.isClose) {
							PaasPostMessage.post('main://popup.back', 1, data);
						}
					}
				} as OpenModel);
			}
		};

		onMounted(() => {
			methods.open();
		});

		return {
			...toRefs(state),
			...components,
			...methods
		};
	}
});
