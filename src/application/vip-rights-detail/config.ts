import { ColumnXtype, TableColumn, TableDateFormat } from '@paas/paas-library';

export function getTableColumn(): TableColumn[] {
	return [
		{
			title: '时间',
			dataIndex: 'createTime',
			dateFormat: TableDateFormat.DAY
		},
		{
			title: '交易类型',
			dataIndex: 'payTypeName'
		},
		{
			title: '交易金额',
			dataIndex: 'money',
			render: data => (data > 0 ? -data : data)
		},
		{
			title: '会员类型',
			dataIndex: 'vipName'
		},
		{
			title: '会员详情',
			dataIndex: 'vipDetail',
			xtype: ColumnXtype.CUSTOM,
			width: 200
		},
		{
			title: '状态',
			dataIndex: 'status',
			xtype: ColumnXtype.CUSTOM,
			useConfigFixed: false
		},
		{
			title: '操作人员',
			dataIndex: 'createUserName'
		}
	];
}
