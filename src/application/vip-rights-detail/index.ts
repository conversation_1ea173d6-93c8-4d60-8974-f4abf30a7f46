import { getTableColumn } from '@/application/vip-rights-detail/config';
import { GetVipRightsDetailStore } from '@/application/vip-rights-detail/store';
import { ColorEnum } from '@/components/text-color/constants';
import { ModelController } from '@paas/paas-library';
import dayjs from 'dayjs';
import { defineComponent } from 'vue';

export default defineComponent({
	setup() {
		const controller = new ModelController({
			table: {
				store: GetVipRightsDetailStore
			}
		});

		const constants = {
			COLUMNS: getTableColumn()
		};

		// 方法
		const methods = {
			getStatusInfo(data) {
				return {
					color:
						[ColorEnum.PINK, ColorEnum.GREEN, ColorEnum.RED, ColorEnum.ORANGE][
							['', '生效中', '已过期', '已退单'].indexOf(data)
						] || ColorEnum.PURPLE,
					dsc: data
				};
			}
		};

		return {
			dayjs,
			controller,
			...constants,
			...methods
		};
	}
});
