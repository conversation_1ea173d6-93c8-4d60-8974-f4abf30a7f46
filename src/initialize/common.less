.operation-content {
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	color: var(--ant-primary-color);
	cursor: pointer;

	:deep(.ant-btn) {
		padding: 4px 5px;

		&:first-child {
			padding-left: 0;
		}
	}
}

.full-loading {
	width: 100px;
	height: 100px;
	background-color: #fff;
	margin: 50vh auto;
	transform: translateY(-50%);
	border-radius: 2px;
	box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);

	.ant-spin {
		width: 100px;
		line-height: 110px;
	}
}

.custom-title-dialog {
	.container {
		display: flex;
		flex-direction: column;
		max-height: calc(100vh - 210px);
		overflow: hidden;

		.header {
			display: flex;
			justify-content: space-between;
			margin-top: -4px;
			padding-bottom: 16px;
			margin-bottom: 20px;
			border-bottom: 1px solid #f0f0f0;

			.title {
				color: rgba(0, 0, 0, 0.85);
				font-weight: 500;
				font-size: 16px;
			}

			.close {
				color: rgba(0, 0, 0, 0.45);
				cursor: pointer;
				font-size: 16px;
			}
		}

		.content {
			display: flex;
			flex: 1;
			height: 0;
			flex-direction: column;
		}
	}
}

// 页面右下角的文本框容器
.body-dialog-text-container {
	position: fixed;
	right: 0;
	bottom: 0;
	z-index: 2000;
	padding: 20px;
	width: 300px;
	height: auto;
	background-color: rgba(255, 255, 255, 0.9);
	border-top-left-radius: 8px;
}
