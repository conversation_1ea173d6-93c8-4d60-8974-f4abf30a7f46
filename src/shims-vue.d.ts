/* eslint-disable */
declare module '*.vue' {
	import '@paas/paas-library/typings/global-components';
	import { ComponentOptions } from 'vue';
	const componentOptions: ComponentOptions;
	export default componentOptions;
}

declare module '@simplex/post-message' {
	type CbType = (...arg: any[]) => void;
	class PostMessage {
		register(protocol: string | CbType, cb?: CbType): void;
		post(protocol: string, ...args: any[]): Promise<any>;
		postView(protocol: string, ...args: any[]): any;
	}

	export = PostMessage;
}
