import { useAccountInfoStore } from '@/pinia/account-info';
import { useRechargeStore } from '@/pinia/recharge';
import { BizSceneEnum } from '@/pinia/recharge/constant';
import { StatusModel } from '@/pinia/recharge/types';
import { copy2Clipboard } from '@/utils/utils';
import { MESSAGE_TYPE, MUtils } from '@paas/paas-library';
import QRCode from 'qrcode';
import { defineComponent, PropType, reactive, toRefs } from 'vue';
import { PayTypeEnum } from './constants';
import { SetPayInfoModel, StateModel } from './types';

export default defineComponent({
	props: {
		title: {
			type: String,
			default: '支付方式'
		},
		showTypes: {
			type: Array as PropType<Array<PayTypeEnum>>,
			default: () => []
		},
		showCmbPrice: {
			type: Boolean,
			default: true
		}
	},

	setup(props, { emit }) {
		const accountInfoStore = useAccountInfoStore();
		const rechargeStore = useRechargeStore();

		const state = reactive<StateModel>({
			// 是否展示支付二维码
			isShowPay: false,
			// 支付方式
			payType: PayTypeEnum.ALIPAY_QR_CODE,
			// 是否勾选协议
			isCheckProtocol: false,
			// 支付金额
			payMoney: 0,
			// 支付二维码
			qrcodeImage: '',
			// 黑名单提示弹窗
			blackListTipsVisible: false,
			// 是否能充值
			canBuy: true,
			// 报错的错误信息
			errorMsg: '',
			// 对公转账信息
			firmOrderData: null
		});

		// 方法
		const methods = {
			/**
			 * 切换支付类型，并且创建订单
			 * @param type
			 */
			handleChangePayType: async (type: PayTypeEnum): Promise<unknown> => {
				state.payType = type;
				emit('changePayType');
				rechargeStore.clearTimer();

				if (!state.isCheckProtocol) {
					state.isShowPay = false;
					return;
				}

				if (type === PayTypeEnum.OTHER) {
					return;
				}

				if (
					state.isShowPay &&
					[PayTypeEnum.ALIPAY_QR_CODE, PayTypeEnum.WEIXIN_QR_CODE, PayTypeEnum.CMB].includes(type)
				) {
					emit('createOrder');
				}
			},
			/**
			 * 支付方式确定按钮
			 */
			payConfirm: () => {
				if (!state.isCheckProtocol) {
					MUtils.toast('请先阅读并勾选《驾考宝典企业版充值服务协议》', MESSAGE_TYPE.error);
				} else {
					state.isShowPay = true;
					emit('createOrder');
				}
			},
			/**
			 * 超时刷新
			 */
			createRecharge: () => {
				emit('createOrder');
			},
			/**
			 * 设置支付信息
			 */
			setPayInfo: async ({
				money = '',
				bizScene,
				openTimeout = true,
				customPayInfo = null,
				customFetchStatus = null
			}: SetPayInfoModel): Promise<void> => {
				state.payMoney = money;
				const data = customPayInfo || rechargeStore.data;

				let params: StatusModel;
				if (
					[PayTypeEnum.BALANCE, PayTypeEnum.ALIPAY_QR_CODE, PayTypeEnum.WEIXIN_QR_CODE].includes(
						state.payType
					)
				) {
					params = {
						orderNumber: data.orderNumber,
						payType: state.payType,
						openTimeout
					};
				}

				switch (state.payType) {
					case PayTypeEnum.BALANCE:
						if (customFetchStatus) {
							await customFetchStatus();
						} else {
							await rechargeStore.fetchStatus(params);
						}
						emit('rechargeEnd', params);
						break;
					case PayTypeEnum.ALIPAY_QR_CODE:
					case PayTypeEnum.WEIXIN_QR_CODE:
						QRCode.toDataURL(data.content).then(url => {
							state.qrcodeImage = url;
						});
						if (customFetchStatus) {
							await customFetchStatus();
						} else {
							await rechargeStore.fetchStatus(params);
						}
						emit('rechargeEnd', params);
						break;
					case PayTypeEnum.CMB:
						// 购买元宝
						if (bizScene === BizSceneEnum.COIN_ORDER_BUY) {
							state.firmOrderData = JSON.parse(data.content);
							return;
						}
						// 充值余额
						if (bizScene === BizSceneEnum.ACCOUNT_RECHARGE) {
							const { cmbMcAccountName, cmbMcAccountNo, cmbMcBankName, cmbTradeCode } =
								accountInfoStore.accountData;
							state.firmOrderData = {
								accountName: cmbMcAccountName,
								accountNo: cmbMcAccountNo + cmbTradeCode,
								bankName: cmbMcBankName
							};
							return;
						}
						// 其余方式，默认从data content取
						state.firmOrderData = JSON.parse(data.content);
						break;
					default:
				}
			},
			copyText: (text: string): void => {
				copy2Clipboard(text, () => {
					MUtils.toast('复制成功', MESSAGE_TYPE.success);
				});
			},
			reset: (): void => {
				rechargeStore.$reset();
				rechargeStore.clearTimer();
				state.isShowPay = false;
				state.payType = PayTypeEnum.ALIPAY_QR_CODE;
				state.isCheckProtocol = false;
				state.payMoney = 0;
				state.qrcodeImage = '';
				state.blackListTipsVisible = false;
				state.canBuy = true;
				state.errorMsg = '';
			}
		};

		const constants = {
			PayTypeEnum
		};

		return {
			accountInfoStore,
			rechargeStore,
			...toRefs(state),
			...methods,
			...constants
		};
	}
});
