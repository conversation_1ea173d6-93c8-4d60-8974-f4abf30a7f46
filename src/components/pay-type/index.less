ul,
ol,
li {
	margin: 0;
	padding: 0;
	list-style: none;
}

.pay-type {
	padding-bottom: 20px;

	.tabs {
		display: flex;

		li {
			width: 110px;
			height: 40px;
			background-color: #fff;
			border: 1px solid var(--border-color-light);
			border-radius: 4px;
			margin-right: 10px;
			box-sizing: border-box;
			cursor: pointer;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 14px;
			color: var(--text-primary);

			&.active {
				background-color: rgba(30, 123, 249, 0.05);
				border-color: #3d8fff;
			}

			&:last-child {
				margin-right: 0;
			}

			.pay-icon {
				width: 18px;
				height: 18px;
				background-position: center;
				background-repeat: no-repeat;
				background-size: cover;
				margin-right: 5px;

				&.amount {
					background-image: url('https://web-resource.mc-cdn.cn/web/qiye-jkbd-v3/business/pay_amount.png!100x0');
				}

				&.ali {
					background-image: url('https://web-resource.mc-cdn.cn/web/qiye-jkbd-v3/business/pay_ali.png!100x0');
				}

				&.wx {
					background-image: url('https://web-resource.mc-cdn.cn/web/qiye-jkbd-v3/business/pay_wx.png!100x0');
				}

				&.cmb {
					background-image: url('https://web-resource.mc-cdn.cn/web/qiye-jkbd-v3/business/pay_cmb.png!100x0');
				}
			}
		}
	}

	.content {
		margin-top: 5px;

		.amount {
			font-size: 14px;
			font-weight: 400;
			color: var(--text-primary);
			line-height: 22px;

			span {
				color: var(--ant-error-color-active);
			}
		}

		.qr-code {
			margin-top: 20px;
			display: flex;
			align-items: center;

			&.timeout {
				position: relative;

				.mask {
					position: absolute;
					content: '';
					display: inline-block;
					width: 100%;
					height: 100%;
					background-color: rgba(255, 255, 255, 0.7);
					backdrop-filter: blur(2px);

					p {
						line-height: 120px;
						text-align: center;

						span {
							color: var(--ant-primary-color);
							cursor: pointer;
						}
					}
				}
			}

			.code {
				width: 120px;
				height: 120px;
				background-color: #fff;
				border: 1px solid var(--border-color-light);
				margin-right: 15px;
				position: relative;

				img {
					width: 100%;
					height: 100%;
				}

				.logo {
					position: absolute;
					left: 50%;
					top: 50%;
					width: 18px;
					height: 18px;
					margin-left: -9px;
					margin-top: -9px;
					background-size: cover;

					&.ali {
						background-image: url('https://web-resource.mucang.cn/web/qiye-jkbd-v3/ali_qr.png');
					}

					&.wx {
						background-image: url('https://web-resource.mucang.cn/web/qiye-jkbd-v3/wx_qr.png');
					}
				}
			}

			.price {
				font-size: 14px;
				font-weight: 400;
				text-align: left;
				color: #666666;
				line-height: 28px;

				span {
					color: var(--ant-error-color);
				}
			}
		}

		.cmb {
			// text-align: center;
			margin: 16px auto 0;

			.title {
				color: var(--ant-error-color);
				margin-bottom: 5px;
			}

			.li-key {
				width: 110px;
			}

			.li-value {
				color: #212121;
			}

			.price {
				font-size: 14px;
				font-weight: 400;
				text-align: left;
				color: #666666;
				line-height: 28px;
				margin: 0;

				span {
					color: var(--ant-error-color);
				}
			}

			ul {
				border: 1px solid #eee;
				border-radius: 2px;

				li {
					display: flex;
					background: #f6f8fa;
					height: 38px;
					line-height: 38px;
					padding: 0px 20px;

					.copy {
						margin-left: auto;
						cursor: pointer;
						color: var(--ant-primary-color);
					}

					&:nth-child(2) {
						background: #fff;
					}
				}
			}
		}

		.other {
			margin-top: 14px;
			margin-bottom: 20px;
			line-height: 28px;
		}

		.error {
			text-align: center;

			img {
				margin-top: 10px;
			}

			p {
				margin-top: 10px;
			}
		}
	}

	.pay-confirm {
		text-align: center;
		margin-top: 25px;

		//&.is-amount {
		//  margin-top: 47px;
		//}

		.ant-btn {
			font-size: 14px;
			line-height: 20px;
			font-weight: 400;
			padding: 0 50px;
		}

		.agreement {
			font-size: 12px;
			font-weight: 400;
			color: var(--color-text-primary);
			line-height: 22px;
			margin-top: 10px;

			a {
				color: #0b79f8;
			}
		}
	}
}
