<template>
	<div class="pay-type">
		<h3>{{ title }}</h3>
		<ul class="tabs">
			<li
				v-if="showTypes.includes(PayTypeEnum.BALANCE)"
				@click="handleChangePayType(PayTypeEnum.BALANCE)"
				:class="{ active: payType === PayTypeEnum.BALANCE }"
			>
				<div class="pay-icon amount" />
				账户余额
			</li>
			<li
				v-if="showTypes.includes(PayTypeEnum.ALIPAY_QR_CODE)"
				@click="handleChangePayType(PayTypeEnum.ALIPAY_QR_CODE)"
				:class="{ active: payType === PayTypeEnum.ALIPAY_QR_CODE }"
			>
				<div class="pay-icon ali" />
				支付宝
			</li>
			<li
				v-if="showTypes.includes(PayTypeEnum.WEIXIN_QR_CODE)"
				@click="handleChangePayType(PayTypeEnum.WEIXIN_QR_CODE)"
				:class="{ active: payType === PayTypeEnum.WEIXIN_QR_CODE }"
			>
				<div class="pay-icon wx" />
				微信支付
			</li>
			<li
				v-if="showTypes.includes(PayTypeEnum.CMB)"
				@click="handleChangePayType(PayTypeEnum.CMB)"
				:class="{ active: payType === PayTypeEnum.CMB }"
			>
				<div class="pay-icon cmb" />
				对公转账
			</li>
			<li
				v-if="showTypes.includes(PayTypeEnum.OTHER)"
				@click="handleChangePayType(PayTypeEnum.OTHER)"
				:class="{ active: payType === PayTypeEnum.OTHER }"
			>
				其它方式
			</li>
		</ul>
		<div class="content">
			<template v-if="!errorMsg">
				<p v-if="payType === PayTypeEnum.BALANCE" class="amount">
					当前账户余额：
					<span>{{ accountInfoStore.accountAmount || '-' }}</span>
				</p>
				<template v-if="isShowPay">
					<div
						v-if="[PayTypeEnum.ALIPAY_QR_CODE, PayTypeEnum.WEIXIN_QR_CODE].includes(payType)"
						:class="['qr-code', rechargeStore.isTimeout ? 'timeout' : '']"
					>
						<div class="code">
							<img :src="qrcodeImage" />
							<div :class="['logo', payType === PayTypeEnum.ALIPAY_QR_CODE ? 'ali' : 'wx']"></div>
						</div>
						<p class="price">
							支付金额：
							<span>{{ payMoney }}元</span>
						</p>
						<div class="mask" v-if="rechargeStore.isTimeout">
							<p>
								支付二维码已过期，点此
								<span @click="createRecharge">刷新</span>
							</p>
						</div>
					</div>
					<div v-if="payType === PayTypeEnum.CMB" class="cmb">
						<div class="title">请使用与入驻驾校名称一致的公户进行打款，否则将原路退回款项，充值失败</div>
						<p class="price" v-if="showCmbPrice">
							支付金额：
							<span>{{ payMoney }}元</span>
						</p>
						<ul v-if="firmOrderData">
							<li>
								<span class="li-key">账户名：</span>
								<span class="li-value">{{ firmOrderData.accountName }}</span>
								<span class="copy" @click="copyText(firmOrderData.accountName)">复制</span>
							</li>
							<li>
								<span class="li-key">订单专用账号：</span>
								<span class="li-value">{{ firmOrderData.accountNo }}</span>
								<span class="copy" @click="copyText(firmOrderData.accountNo)">复制</span>
							</li>
							<li>
								<span class="li-key">开户行：</span>
								<span class="li-value">{{ firmOrderData.bankName }}</span>
								<span class="copy" @click="copyText(firmOrderData.bankName)">复制</span>
							</li>
						</ul>
					</div>
				</template>
				<div v-if="payType === PayTypeEnum.OTHER" class="other">
					<slot>
						<p>
							1、推荐使用上方"支付宝"、"微信支付" 付款, 方便快捷实时到账。
							<br />
							2、网银、公对公转账等其他支付方式请联系杜经理 *********** (微信同号)
						</p>
					</slot>
				</div>
			</template>
			<div v-if="errorMsg" class="error">
				<img src="https://web-resource.mucang.cn/web/qiye-jkbd-v3/money_error_icon.png" alt="警告" />
				<p>{{ errorMsg }}</p>
			</div>
		</div>
		<div
			:class="['pay-confirm', { 'is-amount': payType === PayTypeEnum.BALANCE }]"
			v-if="(!isShowPay || payType === PayTypeEnum.BALANCE) && payType !== PayTypeEnum.OTHER && !errorMsg"
		>
			<m-button type="primary" @click="payConfirm">确定</m-button>
			<div class="fc agreement">
				<m-checkbox v-model:checked="isCheckProtocol">
					请先阅读并勾选
					<a href="https://appstore-paas.jiakaobaodian.com/volvo/xieyi3.html" target="_blank" no="true">
						《驾考宝典企业版充值服务协议》
					</a>
				</m-checkbox>
			</div>
		</div>
	</div>
</template>

<script lang="ts" src="./index.ts"></script>

<style lang="less" src="./index.less"></style>
