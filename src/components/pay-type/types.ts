import { PayTypeEnum } from './constants';
import { BizSceneEnum } from '@/pinia/recharge/constant';
import { RechargeResponse } from '@/pinia/recharge/types';

// 对公转账订单信息
interface FirmOrderModel {
	// 账户名
	accountName: string;
	// 订单专用账号
	accountNo: string;
	// 开户行
	bankName: string;
}

export interface StateModel {
	// 是否展示支付二维码
	isShowPay: boolean;
	// 支付方式
	payType: PayTypeEnum;
	// 是否勾选协议
	isCheckProtocol: boolean;
	// 支付金额
	payMoney: number | string;
	// 支付二维码
	qrcodeImage: string;
	// 黑名单提示弹窗
	blackListTipsVisible: boolean;
	// 是否能充值
	canBuy: boolean;
	// 报错的错误信息
	errorMsg: string;
	// 对公转账信息
	firmOrderData: FirmOrderModel;
}

// 校验订单状态接口
export interface CheckResponse {
	status: number;
}

export type CustomPayInfoModel = Pick<RechargeResponse, 'orderNumber' | 'content'>;

// 设置支付信息
export interface SetPayInfoModel {
	money: string;
	bizScene?: BizSceneEnum;
	openTimeout?: boolean;
	customPayInfo?: CustomPayInfoModel;
	customFetchStatus?: () => unknown;
}
