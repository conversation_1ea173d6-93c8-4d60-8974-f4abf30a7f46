import { defineComponent, reactive, toRefs } from 'vue';
import { MESSAGE_TYPE, MUtils, PaasPostMessage } from '@paas/paas-library';
import { IMAGE_UPLOAD_KEY } from '@/utils/constant';
import Deferred, { DeferredModel } from '@/utils/deferred';
import {
	FetchAgreementSubmitStore,
	GetAgreementDownloadTemplateStore
} from '@/components/payment-agreement-dialog/store';
import { useOpenPreviewImgStore } from '@/pinia/open-preview-img';
import { useAgreementViewStore } from '@/utils/query';

export default defineComponent({
	setup() {
		let dtd: DeferredModel<boolean> = null;

		const openPreviewImgStore = useOpenPreviewImgStore();
		const agreementViewStore = useAgreementViewStore();

		const state = reactive({
			visible: false,
			file: null
		});

		const constants = {};

		const components = {};

		const methods = {
			open(isEdit = false) {
				state.visible = true;

				if (isEdit) {
					state.file = {
						url: agreementViewStore.data.value.fileUrl,
						encodedData: agreementViewStore.data.value.encodeData
					};
				}

				dtd = Deferred();

				return dtd.promise;
			},
			onDownload() {
				GetAgreementDownloadTemplateStore.request()
					.getData()
					.then(data => {
						const url = data.value;

						window.open(url);

						// const link = document.createElement('a');
						// link.href = url;
						// link.download = '委托付款协议.docx';
						// link.style.display = 'none';
						// document.body.appendChild(link);
						// link.click();
						// document.body.removeChild(link);
					});
			},
			onPreview() {
				openPreviewImgStore.open({
					url: 'https://web-resource.mc-cdn.cn/web/qiye-jkbd-v3/invoice/payment-agreement.jpg'
				});
			},
			onConfirm() {
				if (!state.file) {
					MUtils.toast('请先上传协议', MESSAGE_TYPE.warning);

					return;
				}

				FetchAgreementSubmitStore.request({
					encodedData: state.file.encodedData
				})
					.getData()
					.then(() => {
						dtd.resolve(true);

						methods.close();
					});
			},
			onClose() {
				dtd.resolve(false);

				methods.close();
			},
			close() {
				state.visible = false;
				agreementViewStore.refetch();

				methods.reset();
			},
			reset() {
				dtd = null;
			},
			onCustomImageUpload(data) {
				return new Promise((resolve, reject) => {
					PaasPostMessage.post('base://file.upload', {
						files: [data.file],
						...IMAGE_UPLOAD_KEY.volvo
					}).on(res => {
						// todo res 未定义类型
						if (res?.json?.success) {
							const list = res.json?.data || [];
							const item = list.length ? list[0] : {};
							const picData = {
								url: item?.previewUrl,
								encodedData: item?.encodedData
							};
							resolve(picData);
						} else if (res?.json?.success === false) {
							reject();
						}
					});
				});
			}
		};

		return {
			...toRefs(state),
			...constants,
			...components,
			...methods
		};
	}
});
