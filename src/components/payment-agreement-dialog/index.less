.container {
	.agreement-steps {
		.steps-title {
			font-size: 15px;
			font-weight: 500;
			text-align: left;
			color: rgba(0, 0, 0, 0.85);
			line-height: 22px;
			position: relative;
			padding-left: 12px;
			margin-bottom: 15px;

			&::before {
				content: '';
				position: absolute;
				left: 0;
				top: 50%;
				transform: translateY(-50%);
				width: 4px;
				height: 14px;
				background-color: #1e7bf9;
			}
		}

		.steps-list {
			padding-left: 10px;

			.step-item {
				display: flex;
				margin-bottom: 10px;
				line-height: 22px;
				font-size: 14px;
				color: rgba(0, 0, 0, 0.85);

				&:last-child {
					margin-bottom: 0;
				}

				.step-text {
					.download-link,
					.example-link {
						margin-left: 4px;
					}
				}
			}
		}
	}

	.upload-section {
		margin-top: 20px;

		.upload-title {
			font-size: 15px;
			font-weight: 500;
			text-align: left;
			color: rgba(0, 0, 0, 0.85);
			line-height: 22px;
		}

		:deep(.pm-upload-img) {
			margin-top: 10px;
		}

		.upload-tips {
			font-size: 13px;
			text-align: left;
			color: rgba(0, 0, 0, 0.45);
			line-height: 22px;
			margin-top: 10px;
		}
	}
}
