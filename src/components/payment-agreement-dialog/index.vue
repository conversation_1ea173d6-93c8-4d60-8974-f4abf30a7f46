<template>
	<pm-dialog
		v-model:visible="visible"
		title="代付款协议"
		centered
		width="560px"
		:auto-focus="false"
		@close="onClose"
		@confirm="onConfirm"
	>
		<div class="container">
			<div class="agreement-steps">
				<div class="steps-title">签署代付款步骤</div>
				<div class="steps-list">
					<div class="step-item">
						<span class="step-number">1、</span>
						<span class="step-text">
							下载代付款协议模板
							<a href="javascript:;" class="download-link" @click="onDownload">下载模板</a>
						</span>
					</div>
					<div class="step-item">
						<span class="step-number">2、</span>
						<span class="step-text">
							打印后签署，签署要求：甲方盖章，乙方签字并加盖手印
							<a href="javascript:;" class="example-link" @click="onPreview">查看示例</a>
						</span>
					</div>
					<div class="step-item">
						<span class="step-number">3、</span>
						<span class="step-text">拍照/扫描上传彩色文件</span>
					</div>
					<div class="step-item">
						<span class="step-number">4、</span>
						<span class="step-text">完成签署</span>
					</div>
				</div>
			</div>

			<div class="upload-section">
				<div class="upload-title">上传已签署的协议文件</div>
				<pm-upload-img
					v-model:item="file"
					:customUpload="onCustomImageUpload"
					:max-count="10"
					:tip="false"
					remark="点击上传"
				/>
				<div class="upload-tips">
					请上传格式为 JPG 或 PNG 的彩色文件，大小不超过
					10MB，文件需包含章和签字，按收益版本提供，并确保内容完整。
				</div>
			</div>
		</div>
	</pm-dialog>
</template>

<script lang="ts" src="./index.ts"></script>
<style lang="less" src="./index.less" scoped></style>
