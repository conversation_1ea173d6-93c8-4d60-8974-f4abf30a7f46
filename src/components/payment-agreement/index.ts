import { computed, defineComponent, reactive, ref, toRefs } from 'vue';
import PaymentAgreementDialogComp from '@/components/payment-agreement-dialog/index.vue';
import { useUserProfileStore } from '@/pinia/user-profile';
import { useAgreementViewStore } from '@/utils/query';
import { timeFilter } from '@/utils/utils';
import { useOpenPreviewImgStore } from '@/pinia/open-preview-img';
import { GetIsShowInvoiceStore } from '@/components/payment-agreement/store';

export default defineComponent({
	components: {
		PaymentAgreementDialogComp
	},
	props: {
		tip: {
			type: String,
			default: '先签署完协议后才可提交开票'
		}
	},
	setup() {
		const userProfileStore = useUserProfileStore();
		const openPreviewImgStore = useOpenPreviewImgStore();
		const agreementViewStore = useAgreementViewStore();

		const state = reactive({
			visible: false
		});

		const computeds = {
			data: computed(() => {
				return agreementViewStore.data.value;
			}),
			loading: computed(() => {
				return agreementViewStore.isLoading.value;
			}),
			formatExpirationTime: computed(() => {
				if (!agreementViewStore.data.value?.expirationTime) {
					return '';
				}

				return timeFilter(agreementViewStore.data.value.expirationTime, 'YYYY年MM月DD日');
			})
		};

		const constants = {};

		const components = {
			paymentAgreementDialogRef: ref<InstanceType<typeof PaymentAgreementDialogComp>>(null)
		};

		const methods = {
			onView() {
				openPreviewImgStore.open({
					url: agreementViewStore.data.value.fileUrl
				});
			},
			onClick(isEdit: boolean) {
				components.paymentAgreementDialogRef.value.open(isEdit);
			},
			getIsShowInvoice() {
				GetIsShowInvoiceStore.request()
					.getData()
					.then(data => {
						state.visible = !data.value;
					})
					.catch(() => {
						state.visible = false;
					});
			}
		};

		methods.getIsShowInvoice();

		return {
			userProfileStore,
			...toRefs(state),
			...computeds,
			...constants,
			...components,
			...methods
		};
	}
});
