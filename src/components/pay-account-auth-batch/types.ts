import { StatusEnum } from '@/components/pay-account-auth-batch/constants';

export interface StateModel {
	agreement: boolean;
	loading: boolean;
}

export interface PayAccountAuthItemResponse {
	/**  */
	id: number;
	/** 驾校Id */
	jiaxiaoId: number;
	/** 驾校名称 */
	jiaxiaoName: string;
	/** 账户类型 */
	type: string;
	/** 账户类型展示 */
	typeShow: string;
	/** 支付账户 */
	account: string;
	/** 姓名 */
	name: string;
	/** 手机号（掩码） */
	phoneMask: string;
	/** 与驾校关系 */
	relationship: string;
	/** 最近一次的支付时间 */
	lastPayTime: number;
	/** 支付金额（元） */
	lastPayAmount: number;
	/** 购买商品 */
	goodsType: number;
	/** 购买商品展示 */
	goodsTypeShow: string;
	/** 支付流水号 */
	payNumber: string;
	/** 授权状态 0-初始化 1-授权中 2-已授权 3-授权失败 */
	certifiedStatus: StatusEnum;
	/** 更新时间 */
	updateTime: number;
	/** 更新人 */
	updateUserName: string;
}

export interface RequestParamsModel {
	certifiedStatus?: StatusEnum[];
}

export interface EmitModel {
	(e: 'submitAfter'): void;
}
