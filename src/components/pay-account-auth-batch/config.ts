import { StatusEnum } from '@/components/pay-account-auth-batch/constants';
import { PayAccountAuthItemResponse } from '@/components/pay-account-auth-batch/types';
import { ColumnXtype, MESSAGE_TYPE, MoneyUnit, MUtils, TableColumn, TableDateFormat } from '@paas/paas-library';
import { ref } from 'vue';
import { validator } from './utils';

export const updateIdList = ref<Set<number>>(new Set());

export function getTableColumn(): TableColumn[] {
	return [
		{
			title: '支付账户类型',
			dataIndex: 'typeShow'
		},
		{
			title: '支付账户',
			dataIndex: 'account'
		},
		{
			title: '最近一次的支付时间',
			dataIndex: 'lastPayTime',
			dateFormat: TableDateFormat.SECONDS,
			width: 160
		},
		{
			title: '支付金额',
			dataIndex: 'lastPayAmount',
			xtype: ColumnXtype.MONEY,
			moneyConfig: {
				unit: MoneyUnit.YUAN
			}
		},
		{
			title: '购买商品',
			dataIndex: 'goodsTypeShow'
		},
		{
			title: '支付流水号',
			dataIndex: 'payNumber',
			width: 220
		},
		{
			title: '状态',
			dataIndex: 'certifiedStatus',
			xtype: ColumnXtype.CUSTOM,
			width: 155,
			fixed: 'right'
		},
		{
			title: '支付用户姓名',
			dataIndex: 'name',
			xtype: ColumnXtype.INPUT,
			fixed: 'right',
			disable(value, lineData) {
				return [StatusEnum.AUTH, StatusEnum.SUCCESS].includes(lineData.certifiedStatus);
			},
			onChange(value, lineData) {
				updateIdList.value.add(lineData.id);
			},
			antdProps: {
				placeholder: '请输入姓名',
				maxlength: 20
			}
		},
		{
			title: '支付用户手机号',
			dataIndex: 'phoneMask',
			xtype: ColumnXtype.INPUT,
			width: 130,
			useConfigWidth: false,
			fixed: 'right',
			disable(value, lineData) {
				return [StatusEnum.AUTH].includes(lineData.certifiedStatus);
			},
			onChange(value, lineData) {
				updateIdList.value.add(lineData.id);
			},
			antdProps: {
				placeholder: '请输入手机号',
				maxlength: 11
			}
		},
		{
			title: '支付用户与驾校的关系',
			dataIndex: 'relationship',
			xtype: ColumnXtype.CUSTOM,
			width: 260,
			fixed: 'right'
		}
	];
}

// 校验提交数据
export const validatorTable = function (list: PayAccountAuthItemResponse[]) {
	if (!updateIdList.value.size) {
		// if (inDialog) {
		// 	return true;
		// }
		//
		// MUtils.toast('请至少修改一条授权信息', MESSAGE_TYPE.warning);
		// return false;
		return true;
	}

	let errMsg = '';
	const tag = list.some((lineData, index) => {
		if (!updateIdList.value.has(lineData.id)) {
			return false;
		}

		const name = validator.checkName(lineData.name);
		const phone = validator.checkPhone(lineData.phoneMask);
		const connect = validator.checkConnect(lineData.relationship);

		if (name) {
			errMsg += name + '<br>';
		}
		if (phone) {
			errMsg += phone + '<br>';
		}
		if (connect) {
			errMsg += connect + '<br>';
		}

		if (errMsg) {
			errMsg = `<b>第${index + 1}行:</b><br>${errMsg}`;
		}

		return !!errMsg;
	});

	if (tag) {
		MUtils.alert({
			title: '错误提示',
			content: errMsg,
			type: MESSAGE_TYPE.warning
		});

		return false;
	}

	return true;
};

// 获取提交需要的参数
export const getSubmitParams = function (list: PayAccountAuthItemResponse[]) {
	const _list = list.filter(item => updateIdList.value.has(item.id));

	return _list.map(item => ({
		name: item.name,
		phone: item.phoneMask,
		relationship: item.relationship,
		assignedPayAccountId: item.id
	}));
};
