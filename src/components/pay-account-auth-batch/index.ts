import {
	getSubmitParams,
	getTableColumn,
	updateIdList,
	validatorTable
} from '@/components/pay-account-auth-batch/config';
import { STATUS_LIST, StatusEnum } from '@/components/pay-account-auth-batch/constants';
import { FetchBatchAuthStore, GetPayAccountAuthListStore } from '@/components/pay-account-auth-batch/store';
import {
	EmitModel,
	PayAccountAuthItemResponse,
	RequestParamsModel,
	StateModel
} from '@/components/pay-account-auth-batch/types';
import { CONNECT_OPTIONS } from '@/components/pay-account-auth-dialog/constants';
import { ExclamationCircleFilled } from '@ant-design/icons-vue';
import { MESSAGE_TYPE, ModelController, MUtils } from '@paas/paas-library';
import PmTable from '@paas/paas-library/src/components/table';
import { defineComponent, onMounted, PropType, reactive, ref, toRefs } from 'vue';

export default defineComponent({
	components: {
		ExclamationCircleFilled
	},
	props: {
		inDialog: {
			type: Boolean,
			default: false
		},
		requestParams: {
			type: Object as PropType<RequestParamsModel>,
			default: () => {}
		}
	},
	setup(props, { emit }: { emit: EmitModel }) {
		const state = reactive<StateModel>({
			agreement: false,
			loading: false
		});

		const controller = new ModelController({
			table: {
				store: GetPayAccountAuthListStore
			}
		});

		controller.table.onRequest.use(params => {
			params.limit = 10000;
			return params;
		});

		const constants = {
			StatusEnum,
			CONNECT_OPTIONS,
			COLUMNS: getTableColumn()
		};

		const components = {
			pmTableRef: ref<typeof PmTable>(null)
		};

		const methods = {
			onSubmit() {
				MUtils.sendTongji([
					`支付账户批量授权${props.inDialog ? '弹窗' : ''}-提交`,
					'商城中心',
					'商城中心',
					`支付账户批量授权${props.inDialog ? '弹窗' : ''}`
				]);

				if (!state.agreement) {
					MUtils.toast('请先勾选协议内容', MESSAGE_TYPE.warning);
					return;
				}

				const list = components.pmTableRef.value.getTableData();
				const tag = validatorTable(list);
				if (!tag) {
					return;
				}

				const params = getSubmitParams(list);
				state.loading = true;
				FetchBatchAuthStore.request({
					authorizationList: params,
					clearMsgCache: false
				})
					.getData()
					.then(data => {
						MUtils.sendTongji([
							`支付账户批量授权${props.inDialog ? '弹窗' : ''}-提交成功`,
							'商城中心',
							'商城中心',
							`支付账户批量授权${props.inDialog ? '弹窗' : ''}`
						]);

						let content = '';
						let type: MESSAGE_TYPE;
						if (data?.length) {
							content = data.join('; ');
							type = MESSAGE_TYPE.warning;
						} else {
							content = '提交成功，请稍后查看授权结果';
							type = MESSAGE_TYPE.success;
						}

						updateIdList.value.clear();
						controller.tableRequest();

						if (props.inDialog) {
							MUtils.toast(content, type);
							emit('submitAfter');
						} else {
							MUtils.alert({
								title: '提示',
								content,
								type
							}).then(() => {
								emit('submitAfter');
							});
						}
					})
					.finally(() => {
						state.loading = false;
					});
			},
			rowClassName(record) {
				return updateIdList.value.has(record.id) ? 'select' : '';
			},
			getTypeRender(lineData: PayAccountAuthItemResponse) {
				const item = STATUS_LIST.find(v => v.value === lineData.certifiedStatus);

				return {
					color: item.color,
					dsc: item.label
				};
			}
		};

		onMounted(() => {
			MUtils.sendTongji([
				`支付账户批量授权${props.inDialog ? '弹窗' : ''}-展示`,
				'商城中心',
				'商城中心',
				`支付账户批量授权${props.inDialog ? '弹窗' : ''}`
			]);
			updateIdList.value.clear();
			props.inDialog && controller.tableRequest();
		});

		return {
			controller,
			...toRefs(state),
			...constants,
			...components,
			...methods
		};
	}
});
