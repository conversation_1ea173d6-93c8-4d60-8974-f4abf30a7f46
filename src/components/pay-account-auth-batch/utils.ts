export const validator = {
	checkName(value) {
		if (!value) {
			return '支付用户姓名不能为空';
		}
		// if (!/^[\u4e00-\u9fa5_a-zA-Z]{1,20}$/.test(value)) {
		// 	return '支付用户姓名请输入20字符以内的中英文字符';
		// }
		return '';
	},
	checkPhone(value) {
		if (!value) {
			return '支付用户手机号不能为空';
		}
		if (!/^(1|9)[3456789]\d{9}$/.test(value)) {
			return '请输入正确的支付用户手机号';
		}
		return '';
	},
	checkConnect(value) {
		if (!value) {
			return '支付用户与驾校关系不能为空';
		}
		return '';
	}
};
