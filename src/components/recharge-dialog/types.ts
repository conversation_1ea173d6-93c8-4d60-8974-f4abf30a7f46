import { AuthResultEnum } from '@/pinia/open-pay-account-auth/constants';
import { RechargeStatusEnum } from '@/pinia/recharge/constant';

import { ActionEnum } from '@/utils/constants/common';

export interface RechargeModel {
	visible: boolean; //弹框的展示
	codeLoading: boolean; //加载二维码
	moneyArr: number[];
	selectMoneyTabIndex: number;
	inputMoney: string; //输入的金额
	money: number | string;
	checkMoneyLimit: boolean;
	callback: (data: RechargeCallbackDataModel) => unknown;
}

export interface OpenModel extends Partial<Pick<RechargeModel, 'callback'>> {
	amount?: string;
	beforeCallback?: () => unknown;
}

export interface RechargeCallbackDataModel {
	status?: RechargeStatusEnum;
	accountAuthStatus?: AuthResultEnum;
	action: ActionEnum;
	isClose: boolean;
}

export interface RechargeResponse {
	appId: number;
	content: string;
	orderNumber: string;
}
