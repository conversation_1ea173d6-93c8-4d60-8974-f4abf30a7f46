import { PayTypeEnum } from '@/components/pay-type/constants';
import { useAccountInfoStore } from '@/pinia/account-info';
import { useOpenPayAccountAuthStore } from '@/pinia/open-pay-account-auth';
import { GoodsTypeEnum } from '@/pinia/open-pay-account-auth/constants';
import { AccountAuthCallbackDataModel } from '@/pinia/open-pay-account-auth/types';
import { useRechargeStore } from '@/pinia/recharge';
import { BizSceneEnum, PaySubTypeEnum, RechargeStatusEnum, SourceEnum } from '@/pinia/recharge/constant';
import { StatusModel } from '@/pinia/recharge/types';
import { REG_NATURAL_NUMBER } from '@/utils/util-reg';
import { MESSAGE_TYPE, MUtils, PaasPostMessage } from '@paas/paas-library';
import { defineComponent, onMounted, reactive, ref, toRefs, watch } from 'vue';
import PayTypeComp from '../pay-type/index.vue';
import { MONEY_LIST } from './constant';
import { OpenModel, RechargeModel } from './types';
import { ActionEnum } from '@/utils/constants/common';

export default defineComponent({
	components: {
		PayTypeComp
	},

	setup() {
		const components = {
			payTypeRef: ref<typeof PayTypeComp>()
		};

		const constants = {
			MONEY_LIST,
			PayTypeEnum,
			ActionEnum
		};

		const accountInfoStore = useAccountInfoStore();
		const rechargeStore = useRechargeStore();
		const openPayAccountAuthStore = useOpenPayAccountAuthStore();

		const state = reactive<RechargeModel>({
			visible: false, //弹框的展示
			codeLoading: false, //加载二维码
			moneyArr: constants.MONEY_LIST,
			selectMoneyTabIndex: 0,
			inputMoney: '', //输入的金额
			money: constants.MONEY_LIST[0],
			checkMoneyLimit: true,
			callback: () => {}
		});

		watch(
			() => state.inputMoney,
			(newVal: string) => {
				methods.onInputMoneyChange(newVal);
			}
		);

		const methods = {
			async checkShow({ amount, beforeCallback, callback }: OpenModel = {}) {
				console.log('recharge checkOpen', amount);
				await methods.show();
				if (amount) {
					state.selectMoneyTabIndex = state.moneyArr.length;
					state.checkMoneyLimit = false;
					state.inputMoney = String(amount);
				}
				typeof beforeCallback === 'function' && beforeCallback();
				state.callback = callback;
			},

			show: async (money: number = MONEY_LIST[0]): Promise<void> => {
				await methods.setTestStatus();
				const moneyIndex = state.moneyArr.indexOf(money);
				state.visible = true;
				state.selectMoneyTabIndex = moneyIndex < 0 ? state.moneyArr.length : moneyIndex;
				state.money = money;
				state.inputMoney = moneyIndex < 0 ? `${money}` : '';

				methods.checkPermission();
			},

			async checkPermission() {
				rechargeStore.$reset();
				await rechargeStore.fetchRecharge({
					amount: 0.01,
					paySubType: PaySubTypeEnum.ALIPAY_QR_CODE,
					source: SourceEnum.PAAS,
					bizScene: BizSceneEnum.ACCOUNT_RECHARGE
				});

				if (rechargeStore.isError) {
					const options = {
						title: '提示',
						content: rechargeStore.error.message,
						btText: '确定',
						type: MESSAGE_TYPE.error
					};
					MUtils.alert(options).then(() => {
						typeof state.callback === 'function' &&
							state.callback({
								status: rechargeStore.status,
								action: ActionEnum.AUTO,
								isClose: true
							});
					});
				}
			},

			initEvent: (): void => {},

			reset(): void {
				state.codeLoading = false;
				state.moneyArr = constants.MONEY_LIST;
				state.selectMoneyTabIndex = 0;
				state.inputMoney = '';
				state.money = constants.MONEY_LIST[0];
				state.checkMoneyLimit = true;
				state.callback = () => {};
			},

			close(action: ActionEnum = ActionEnum.AUTO): void {
				const callback = state.callback;

				components.payTypeRef.value?.reset();
				methods.reset();
				state.visible = false;

				if (action === ActionEnum.CLOSE) {
					typeof callback === 'function' &&
						callback({
							action: ActionEnum.CLOSE,
							isClose: true
						});
				}
			},

			/**
			 * 输入的金额变化时
			 */
			onInputMoneyChange: MUtils.debounce((money: string): void => {
				state.money = money;
				state.visible && methods.createOrder();
			}, 500),

			/**
			 * 充值金额切换
			 */
			moneyTabChange: (tabIndex: number, money: number | null): void => {
				if (state.selectMoneyTabIndex === tabIndex) {
					return;
				}

				if (money) {
					state.selectMoneyTabIndex = tabIndex;
					state.money = money;
				} else {
					state.selectMoneyTabIndex = tabIndex;
					state.money = state.inputMoney;
				}

				methods.createOrder();
			},

			/**
			 * 创建订单
			 */
			createOrder: async (): Promise<unknown> => {
				const payTypeRef = components.payTypeRef.value;
				payTypeRef.errorMsg = '';

				const money = state.money;
				const payType = payTypeRef.payType as PayTypeEnum;

				if (!payTypeRef.isShowPay) {
					return;
				}
				if (payType !== PayTypeEnum.CMB) {
					if (!money) {
						payTypeRef.errorMsg = '请输入或选择充值金额！';
						return;
					}
					if (state.checkMoneyLimit && money < 100) {
						payTypeRef.errorMsg = '请输入不少于100的充值金额！';
						return;
					}
					if (money > 999999) {
						payTypeRef.errorMsg = '请输入不高于999999的充值金额！';
						return;
					}
					if (state.checkMoneyLimit && !REG_NATURAL_NUMBER.test(String(money))) {
						payTypeRef.errorMsg = '充值金额只能为正整数!';
						return;
					}
					if (['0', '0.0', '0.00'].includes(String(money))) {
						payTypeRef.errorMsg = '充值金额只能为正整数!';
						return;
					}
					if ([PayTypeEnum.BALANCE, PayTypeEnum.OTHER].includes(payType)) {
						return;
					}
				}

				// 创建充值订单
				rechargeStore.$reset();
				await rechargeStore.fetchRecharge({
					amount: money,
					paySubType: PaySubTypeEnum[payType],
					source: SourceEnum.PAAS,
					bizScene: BizSceneEnum.ACCOUNT_RECHARGE
				});

				if (rechargeStore.isSuccess) {
					payTypeRef.setPayInfo({
						money,
						bizScene: BizSceneEnum.ACCOUNT_RECHARGE
					});
				}

				if (rechargeStore.isError) {
					const options = {
						title: '提示',
						content: rechargeStore.error.message,
						btText: '确定',
						type: MESSAGE_TYPE.error
					};
					MUtils.alert(options);
				}
			},

			// 充值结束
			rechargeEnd: (params: StatusModel) => {
				let isClose = false;
				if (rechargeStore.status === RechargeStatusEnum.SUCCESS) {
					const callback = state.callback;
					const status = rechargeStore.status;
					MUtils.toast('充值成功', MESSAGE_TYPE.success);
					isClose = true;
					methods.close();

					openPayAccountAuthStore.open({
						...params,
						goodsType: GoodsTypeEnum.RECHARGE,
						callback: (data: AccountAuthCallbackDataModel) => {
							accountInfoStore.fetchAccountAndCoinInfo();
							typeof callback === 'function' &&
								callback({
									status,
									accountAuthStatus: data.status,
									action: data.action,
									isClose
								});
						}
					});

					return;
				} else if (rechargeStore.status === RechargeStatusEnum.FAIL) {
					MUtils.toast('充值失败，请稍后重试', MESSAGE_TYPE.error);
					methods.createOrder();
				}

				typeof state.callback === 'function' &&
					state.callback({
						status: rechargeStore.status,
						action: ActionEnum.AUTO,
						isClose
					});
			},

			onChangePayType: (): void => {
				methods.createOrder();
			},

			onConfirmPayType: (): void => {
				methods.createOrder();
			},

			async setTestStatus() {
				try {
					const data = await PaasPostMessage.post('main://location');
					state.checkMoneyLimit = data.search.indexOf('isTestRecharge') < 0;
				} catch (e) {
					state.checkMoneyLimit = true;
				}
			}
		};

		onMounted(() => {
			methods.initEvent();
		});

		return {
			rechargeStore,
			...components,
			...constants,
			...toRefs(state),
			...methods
		};
	}
});
