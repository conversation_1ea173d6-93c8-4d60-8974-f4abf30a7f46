<template>
	<pm-dialog
		v-model:visible="visible"
		title="账户充值"
		centered
		width="750px"
		:auto-focus="false"
		:footer="null"
		@close="close(ActionEnum.CLOSE)"
	>
		<m-spin :spinning="rechargeStore.isLoading">
			<div class="dialog-content">
				<div class="invest-money">
					<h3>
						<span>充值金额</span>
						充值金额须在100～999999元之间的整数
					</h3>
					<p class="warning">该渠道充值的金额仅可购买会员或元宝，如需购买其它商品，请联系销售或售后人员！</p>
					<div class="money-count">
						<div
							v-for="(item, index) in moneyArr"
							:key="item"
							:class="`money-btn ${selectMoneyTabIndex === index ? 'active' : ''}`"
							@click="moneyTabChange(index, item)"
						>
							{{ item }}元
						</div>
						<div class="money-input">
							<span>其他金额</span>
							<input
								v-model="inputMoney"
								:class="selectMoneyTabIndex === moneyArr.length && 'active'"
								type="text"
								placeholder="请输入充值金额"
								@click="moneyTabChange(moneyArr.length, null)"
							/>
						</div>
					</div>
				</div>

				<pay-type-comp
					v-if="visible"
					ref="payTypeRef"
					@createOrder="createOrder"
					@rechargeEnd="rechargeEnd"
					:show-types="[
						PayTypeEnum.ALIPAY_QR_CODE,
						PayTypeEnum.WEIXIN_QR_CODE,
						PayTypeEnum.CMB,
						PayTypeEnum.OTHER
					]"
					:show-cmb-price="false"
				/>

				<div class="invest-faq">
					<h2>常见问题</h2>
					<div class="question-list">
						<div class="question">
							<i />
							充值未到账怎么办？
						</div>
						<div class="answer answer2">
							一般充值后会即刻发放到您的账户。如遇服务器拥堵，到账可能有短时间的延迟，建议耐心等待，并刷新余额界面查看到账情况。
						</div>
						<div class="answer">若超过30分钟仍未到账，请您联系官方客服400-056-1600。</div>
						<div class="question">
							<i />
							误充或充错了，可以退款吗？
						</div>
						<div class="answer">
							误充或充错后，不能进行退款哦。建议您在充值前仔细核对充值信息，以避免发生误充、充错的情况。
						</div>
						<div class="question">
							<i />
							充值后，可以转移到其他的账号上吗？
						</div>
						<div class="answer">账户里的余额、会员、线索等资产均无法在帐号之间进行转移。</div>
					</div>
				</div>
			</div>
		</m-spin>
	</pm-dialog>
</template>

<script lang="ts" src="./index.ts"></script>

<style lang="less" src="./index.less" scoped></style>
