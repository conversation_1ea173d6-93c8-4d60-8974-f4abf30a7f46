import { useOpenPreviewImgStore } from '@/pinia/open-preview-img';
import { TypeEnum } from '@/pinia/open-preview-img/constants';
import { CloseCircleOutlined } from '@ant-design/icons-vue';
import { defineComponent, ref } from 'vue';

export default defineComponent({
	components: {
		CloseCircleOutlined
	},
	setup() {
		const openPreviewImgStore = useOpenPreviewImgStore();

		const components = {
			videoRef: ref<HTMLVideoElement>(null)
		};

		const constants = {
			TypeEnum
		};

		const methods = {
			onCloseVideo() {
				components.videoRef.value.pause();
				openPreviewImgStore.onChange(false);
			}
		};

		return {
			openPreviewImgStore,
			...components,
			...constants,
			...methods
		};
	}
});
