<template>
	<pm-dialog
		v-model:visible="openBuyCoinStore.visible"
		title="元宝购买"
		:centered="false"
		width="520px"
		:auto-focus="false"
		:footer="null"
		@close="close(ActionEnum.CLOSE)"
		destroy-on-close
	>
		<m-spin :spinning="showedLoading" :tip="loadingTip">
			<div class="dialog-content">
				<div class="coin-count">
					<h3>请选择要充值的元宝</h3>
					<ul class="coin-list">
						<li
							:class="['item', selectGoodsCode === item.goodsCode ? 'active' : '']"
							v-for="item in accountInfoStore.goldArr"
							:key="item.goodsCode"
							@click="handleChangeCoinCount(item.goodsCode)"
						>
							<p class="coin">{{ item.coinCount }}元宝</p>
							<p class="price">¥ {{ item.goodsPrice }}</p>
						</li>
						<!-- <li :class="['item', isCustomCount ? 'active' : '']" @click="handleChangeCoinCount(inputCount)">
							<p class="coin">
								<m-input
									type="text"
									:bordered="false"
									v-model:value="inputCount"
									@input="handleInput"
									placeholder="自定义元宝"
								/>
							</p>
							<p class="price">¥ {{ inputPayMoney }}</p>
						</li> -->
					</ul>
				</div>

				<pay-type-comp
					v-if="openBuyCoinStore.visible"
					ref="payTypeRef"
					@createOrder="createOrder"
					@rechargeEnd="rechargeEnd"
					:show-types="[
						PayTypeEnum.BALANCE,
						PayTypeEnum.ALIPAY_QR_CODE,
						PayTypeEnum.WEIXIN_QR_CODE,
						PayTypeEnum.CMB
					]"
				/>

				<div class="tips">
					<h3>温馨提示：</h3>
					<p class="info">1.兑换等值：1元宝={{ accountInfoStore.coinPrice }}元</p>
					<p class="info">2.元宝仅可用于购买线索，充值元宝前请前去开通招生宝或电话线索服务；</p>
					<p class="info">3.若账户余额足以支付要充值的元宝数，将会自动抵扣。</p>
				</div>
			</div>
		</m-spin>
	</pm-dialog>
</template>

<script lang="ts" src="./index.ts"></script>
<style lang="less" src="./index.less" scoped></style>
