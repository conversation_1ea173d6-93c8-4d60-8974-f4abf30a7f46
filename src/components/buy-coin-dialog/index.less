ul,
ol,
li {
	margin: 0;
	padding: 0;
	list-style: none;
}

.dialog-content {
	padding: 0 5px;

	.coin-count {
		h3 {
			font-size: 16px;
			margin: 0 0 16px;
		}

		.coin-list {
			display: grid;
			grid-template-columns: repeat(4, 1fr);
			grid-column-gap: 10px;
			grid-row-gap: 10px;
			margin-bottom: 16px;

			.item {
				text-align: center;
				background-color: var(--color-white);
				border: 1px solid var(--border-color-light);
				border-radius: 4px;
				box-sizing: border-box;
				cursor: pointer;
				color: var(--color-text-primary);

				&.active {
					background-color: rgba(30, 123, 249, 0.05);
					border-color: #3d8fff;

					& > p {
						&.coin {
							color: #3d8fff;

							input {
								color: #3d8fff;
							}
						}
					}
				}

				& > p {
					padding: 6px 0;
				}

				.coin {
					font-size: 18px;
					line-height: 24px;
					border-bottom: 1px dashed var(--border-color-light);
					margin: 0;

					:deep(.ant-input) {
						width: 100%;
						height: 24px;
						font-size: 18px;
						text-align: center;
						outline: none;
						background: none;

						&:hover,
						&:focus {
							border: none !important;
							box-shadow: none !important;
						}

						&::-webkit-input-placeholder {
							font-size: 14px;
						}

						&:-moz-placeholder {
							font-size: 14px;
						}

						&::-moz-placeholder {
							font-size: 14px;
						}

						&:-ms-input-placeholder {
							font-size: 14px;
						}
					}
				}

				.price {
					font-size: 14px;
					margin: 0;
				}
			}
		}
	}

	.tips {
		h3 {
			margin-bottom: 10px;
		}

		.info {
			font-size: 13px;
			font-weight: 400;
			color: rgba(143, 147, 161, 1);
			line-height: 22px;
			margin: 0;
		}
	}
}
