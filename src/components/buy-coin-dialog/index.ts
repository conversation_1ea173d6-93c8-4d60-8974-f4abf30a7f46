import { PayTypeEnum } from '@/components/pay-type/constants';
import PayTypeComp from '@/components/pay-type/index.vue';
import { useAccountInfoStore } from '@/pinia/account-info';
import { useBlackListStore } from '@/pinia/black-list';
import { useOpenBuyCoinStore } from '@/pinia/open-buy-coin';
import { useOpenPayAccountAuthStore } from '@/pinia/open-pay-account-auth';
import { GoodsTypeEnum } from '@/pinia/open-pay-account-auth/constants';
import { AccountAuthCallbackDataModel } from '@/pinia/open-pay-account-auth/types';
import { useRechargeStore } from '@/pinia/recharge';
import { BizSceneEnum, PaySubTypeEnum, RechargeStatusEnum, SourceEnum } from '@/pinia/recharge/constant';
import { StatusModel } from '@/pinia/recharge/types';
import { multiply } from '@/utils/utils';
import { MESSAGE_TYPE, MUtils, PaasPostMessage } from '@paas/paas-library';
import { computed, defineComponent, onMounted, reactive, ref, toRefs, watch } from 'vue';
import { StateModel } from './types';
import { ActionEnum } from '@/utils/constants/common';

export default defineComponent({
	components: {
		PayTypeComp
	},
	setup() {
		const accountInfoStore = useAccountInfoStore();
		const rechargeStore = useRechargeStore();
		const blackListStore = useBlackListStore();
		const openBuyCoinStore = useOpenBuyCoinStore();
		const openPayAccountAuthStore = useOpenPayAccountAuthStore();

		const state = reactive<StateModel>({
			isTest: false,
			// 选中的元宝数量
			selectGoodsCode: null,
			// 自定义元宝输入的数量
			inputCount: ''
		});

		const constants = {
			PayTypeEnum,
			ActionEnum
		};

		const components = {
			payTypeRef: ref<typeof PayTypeComp>(null)
		};

		const computeds = {
			// 是否自定义价格
			isCustomCount: computed(() => {
				// return !accountInfoStore.goldArr.includes(state.selectGoodsCode);

				return false;
			}),
			inputPayMoney: computed(() => {
				if (isNaN(Number(state.inputCount))) {
					return '-';
				}
				if (!accountInfoStore.coinPrice) {
					return '-';
				}
				return multiply(state.inputCount, accountInfoStore.coinPrice);
			}),
			// 需要支付的金额
			payMoney: computed(() => {
				const goods = accountInfoStore.goldArr.find(item => item.goodsCode === state.selectGoodsCode);

				return goods ? goods.goodsPrice : '-';
			}),
			// 千分位格式化金额
			payMoneyPlus: computed(() => {
				return computeds.payMoney.value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
			}),
			// 是否展示loading
			showedLoading: computed(() => {
				const payTypeRef = components.payTypeRef.value;
				const payType = payTypeRef?.payType;

				return rechargeStore.isLoading || (payType === PayTypeEnum.BALANCE && rechargeStore.statusLoading);
			}),
			loadingTip: computed(() => {
				const payTypeRef = components.payTypeRef.value;
				const payType = payTypeRef?.payType;

				return payType === PayTypeEnum.BALANCE && rechargeStore.statusLoading
					? '支付结果查询中...'
					: '加载中...';
			})
		};

		watch(
			() => openBuyCoinStore.visible,
			() => {
				// 展开弹窗时，
				if (openBuyCoinStore.visible) {
					const goods = accountInfoStore.goldArr[0];

					// 设置默认选中的元宝数量
					state.selectGoodsCode = goods.goodsCode;

					// 动态设置默认支付方式
					setTimeout(() => {
						const needAmount = goods.goodsPrice;

						if (accountInfoStore.accountAmount > needAmount) {
							components.payTypeRef.value.payType = PayTypeEnum.BALANCE;
						}
					}, 100);
				}
			}
		);

		// 方法
		const methods = {
			// 切换充值金额
			handleChangeCoinCount(goodsCode: string) {
				state.selectGoodsCode = goodsCode;

				rechargeStore.$reset();
				rechargeStore.clearTimer();

				const payTypeRef = components.payTypeRef.value;
				payTypeRef.isShowPay = false;
				payTypeRef.errorMsg = '';
			},
			// // 自定义元宝数量
			// handleInput() {
			// 	state.selectGoodsCode = Number(state.inputCount);
			// },
			// 金额大于等于1w时出现二次确认弹框
			largeMoneyConfirm(money): Promise<boolean> {
				return new Promise<boolean>(resolve => {
					if (money >= 10000) {
						components.payTypeRef.value.isShowPay = false;
						MUtils.confirm({
							title: '提示',
							content: `您本次充值元宝${state.selectGoodsCode}个，合计金额 <span style="color: var(--color-danger);font-size: 24px;font-weight: 500;">${computeds.payMoneyPlus.value}元</span>，确认是否购买？`,
							type: MESSAGE_TYPE.warning
						}).then(bool => {
							if (!bool) {
								resolve(false);
								return;
							}
							components.payTypeRef.value.isShowPay = true;
							resolve(true);
						});
					} else {
						resolve(true);
					}
				});
			},
			// 创建订单
			async createOrder() {
				const payTypeRef = components.payTypeRef.value;
				const payType = payTypeRef.payType as PayTypeEnum;
				payTypeRef.errorMsg = '';

				const money = computeds.payMoney.value;

				if (!payTypeRef.isShowPay) {
					return;
				}

				// 如果是自定义元宝数
				// if (computeds.isCustomCount.value) {
				// 	if (!state.inputCount) {
				// 		payTypeRef.errorMsg = '请输入或选择充值金额！';
				// 		return;
				// 	}

				// 	const flag = /^[1-9]\d{0,3}$/.test(state.inputCount) && Number(state.inputCount) > 99;
				// 	if (!flag && !state.isTest) {
				// 		payTypeRef.errorMsg = '购买数量只能为100-9999内整数！';
				// 		return;
				// 	}
				// }

				if (money === '-') {
					payTypeRef.errorMsg = '购买数量只能为100-9999内整数！';
					return;
				}

				if (payType === PayTypeEnum.BALANCE) {
					if (accountInfoStore.accountAmount < money) {
						MUtils.toast('当前账户余额不足，请选择其它支付方式', MESSAGE_TYPE.error);
						return;
					}
				}

				if (payType !== PayTypeEnum.BALANCE && !blackListStore.checkBuy()) {
					return;
				}

				const flagConfirm = await methods.largeMoneyConfirm(money);

				// 取消时直接关闭弹框
				if (!flagConfirm) {
					return;
				}

				// 创建充值订单
				rechargeStore.$reset();
				await rechargeStore.fetchRecharge({
					amount: money,
					goodsCode: state.selectGoodsCode,
					paySubType: PaySubTypeEnum[payType],
					source: SourceEnum.PAAS,
					bizScene: BizSceneEnum.COIN_ORDER_BUY,
					coinCount: accountInfoStore.goldArr.find(item => item.goodsCode === state.selectGoodsCode)
						?.coinCount
				});

				// amount: state.currentGoodsInfo.price,
				// 	goodsCode: state.currentGoodsInfo.goodsCode,
				// 	paySubType: PaySubTypeEnum[payType],
				// 	source: SourceEnum.PAAS,
				// 	bizScene: BizSceneEnum.LEAD_PACK_BUY

				if (rechargeStore.isSuccess) {
					payTypeRef.setPayInfo({
						money,
						bizScene: BizSceneEnum.COIN_ORDER_BUY
					});
				}

				if (rechargeStore.isError) {
					const options = {
						title: '提示',
						content: rechargeStore.error.message,
						btText: '确定',
						type: MESSAGE_TYPE.error
					};
					MUtils.alert(options);
				}
			},
			// 充值结束
			rechargeEnd(params: StatusModel) {
				let isClose = false;
				if (rechargeStore.status === RechargeStatusEnum.SUCCESS) {
					const callback = openBuyCoinStore.callback;
					const status = rechargeStore.status;
					MUtils.toast('充值成功', MESSAGE_TYPE.success);
					isClose = true;
					methods.close();

					openPayAccountAuthStore.open({
						...params,
						goodsType: GoodsTypeEnum.BUY_COIN,
						callback: (data: AccountAuthCallbackDataModel) => {
							accountInfoStore.fetchAccountAndCoinInfo();
							typeof callback === 'function' &&
								callback({
									status,
									action: ActionEnum.AUTO,
									accountAuthStatus: data.status,
									isClose
								});
						}
					});

					return;
				} else if (rechargeStore.status === RechargeStatusEnum.FAIL) {
					MUtils.toast('充值失败，请稍后重试', MESSAGE_TYPE.error);

					const payType = components.payTypeRef.value.payType as PayTypeEnum;
					if ([PayTypeEnum.ALIPAY_QR_CODE, PayTypeEnum.WEIXIN_QR_CODE, PayTypeEnum.CMB].includes(payType)) {
						// 支付宝、微信、对公转账 重新创建订单
						methods.createOrder();
					} else if ([PayTypeEnum.BALANCE].includes(payType)) {
						// 余额购买 刷新账户信息
						accountInfoStore.fetchAccountAndCoinInfo();
					}
				}

				typeof openBuyCoinStore.callback === 'function' &&
					openBuyCoinStore.callback({
						status: rechargeStore.status,
						action: ActionEnum.AUTO,
						isClose
					});
			},

			reset() {
				state.isTest = false;
				state.selectGoodsCode = null;
				state.inputCount = '';
			},

			close(action: ActionEnum = ActionEnum.AUTO) {
				const callback = openBuyCoinStore.callback;

				components.payTypeRef.value?.reset();
				methods.reset();
				openBuyCoinStore.$reset();

				if (action === ActionEnum.CLOSE) {
					typeof callback === 'function' &&
						callback({
							action: ActionEnum.CLOSE,
							isClose: true
						});
				}

				// test
				// openPayAccountAuthStore.open({
				// 	orderNumber: '2023081419100100799648084',
				// 	payType: PayTypeEnum.ALIPAY_QR_CODE
				// });
			},

			async setTestStatus() {
				try {
					const data = await PaasPostMessage.post('main://location');
					state.isTest = data.search.indexOf('isTestRecharge') >= 0;
				} catch (e) {
					state.isTest = false;
				}
			}
		};

		onMounted(() => {
			methods.setTestStatus();
		});

		return {
			accountInfoStore,
			rechargeStore,
			openBuyCoinStore,
			...toRefs(state),
			...computeds,
			...constants,
			...components,
			...methods
		};
	}
});
