import { CONNECT_OPTIONS } from '@/components/pay-account-auth-dialog/constants';
import { FetchPayAccountAuthPayNumberStore } from '@/components/pay-account-auth-dialog/store';
import { PayAccountAuthParamsModel, StateModel } from '@/components/pay-account-auth-dialog/types';
import { validateName } from '@/components/pay-account-auth-dialog/utils';
import { useOpenPayAccountAuthStore } from '@/pinia/open-pay-account-auth';
import { AuthResultEnum } from '@/pinia/open-pay-account-auth/constants';
import { validatePhone } from '@/utils/util-validate';
import { ExclamationCircleFilled } from '@ant-design/icons-vue';
import { MESSAGE_TYPE, MUtils } from '@paas/paas-library';
import type { Rule } from 'ant-design-vue/es/form';
import { defineComponent, reactive, toRefs } from 'vue';
import { ActionEnum } from '@/utils/constants/common';

export default defineComponent({
	components: {
		ExclamationCircleFilled
	},
	setup() {
		const openPayAccountAuthStore = useOpenPayAccountAuthStore();

		const state = reactive<StateModel>({
			authLoading: false,
			formState: {
				name: '',
				phone: '',
				relationship: undefined
			}
		});

		const constants = {
			CONNECT_OPTIONS,
			ActionEnum
		};

		const methods = {
			onFinish() {
				MUtils.sendTongji([
					`${openPayAccountAuthStore.goodsType}-提交`,
					'线索管理',
					'购买线索',
					'支付账户授权弹窗'
				]);

				const params: PayAccountAuthParamsModel = {
					...state.formState,
					payNumber: openPayAccountAuthStore.payNumber
				};

				state.authLoading = true;
				FetchPayAccountAuthPayNumberStore.request(params)
					.getData()
					.then(async data => {
						MUtils.sendTongji([
							`${openPayAccountAuthStore.goodsType}-提交成功`,
							'线索管理',
							'购买线索',
							'支付账户授权弹窗'
						]);

						const id = data.value;

						await openPayAccountAuthStore.fetchStatus({
							id
						});

						if (openPayAccountAuthStore.status === AuthResultEnum.SUCCESS) {
							MUtils.toast('授权成功', MESSAGE_TYPE.success);
						}
						if (openPayAccountAuthStore.status === AuthResultEnum.FAIL) {
							MUtils.toast('授权失败，请稍后重试', MESSAGE_TYPE.error);
						}
						if (openPayAccountAuthStore.status === AuthResultEnum.ING) {
							MUtils.toast('授权超时，请稍后在商城中心查询授权结果', MESSAGE_TYPE.warning);
						}
						typeof openPayAccountAuthStore.callback === 'function' &&
							openPayAccountAuthStore.callback({
								status: openPayAccountAuthStore.status,
								isNeedAuth: true,
								action: ActionEnum.AUTO
							});
						methods.onClose();
					})
					.finally(() => {
						state.authLoading = false;
					});
			},
			onClose(action: ActionEnum = ActionEnum.AUTO) {
				const callback = openPayAccountAuthStore.callback;

				methods.reset();
				openPayAccountAuthStore.$reset();
				openPayAccountAuthStore.clearTimer();

				if (action === ActionEnum.CLOSE) {
					typeof callback === 'function' &&
						callback({
							status: openPayAccountAuthStore.status,
							isNeedAuth: true,
							action: ActionEnum.CLOSE
						});
				}
			},
			reset() {
				state.formState = {
					name: '',
					phone: '',
					relationship: undefined
				};
			}
		};

		const rules: Record<string, Rule[]> = {
			name: [{ required: true, validator: validateName, trigger: 'blur' }],
			phone: [{ required: true, validator: validatePhone, trigger: 'blur' }],
			relationship: [{ required: true, message: '请选择充值账户与绑定驾校的关系', trigger: 'change' }]
		};

		return {
			openPayAccountAuthStore,
			rules,
			...toRefs(state),
			...constants,
			...methods
		};
	}
});
