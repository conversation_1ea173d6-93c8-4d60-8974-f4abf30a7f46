<template>
	<pm-dialog
		v-model:visible="openPayAccountAuthStore.visible"
		title="支付账户授权"
		centered
		width="600px"
		:auto-focus="false"
		:footer="null"
		@close="onClose(ActionEnum.CLOSE)"
		destroy-on-close
	>
		<m-spin :spinning="openPayAccountAuthStore.statusLoading" tip="授权结果查询中...">
			<div class="container">
				<p class="money" v-if="openPayAccountAuthStore.amount">
					您已成功充值
					<span>{{ openPayAccountAuthStore.amount }}</span>
					元
				</p>
				<p class="remark">
					由于该充值的账户未与当前驾校绑定，充值后金额将冻结，为保证您的充值信息安全，请补充充值账户相关信息，平台根据您填写的信息打款验证后，会自动绑定本次充值用户为您当前驾校的可充值用户，后期可正常充值。
				</p>
				<ul class="info">
					<li>
						<label>本次支付账户类型：</label>
						{{ openPayAccountAuthStore.typeShow }}
					</li>
					<li v-if="openPayAccountAuthStore.account">
						<label>本次支付账户：</label>
						{{ openPayAccountAuthStore.account }}
					</li>
				</ul>
				<div class="content">
					<h3>需补充的充值账户信息如下：</h3>
					<m-form
						:model="formState"
						:rules="rules"
						:label-col="{ span: 9 }"
						:wrapper-col="{ span: 15 }"
						autocomplete="off"
						@finish="onFinish"
					>
						<m-form-item label="充值账户真实姓名" name="name">
							<m-input
								class="tips-input"
								v-model:value="formState.name"
								:maxlength="20"
								placeholder="请输入您的真实姓名"
							/>
							<p class="tips">
								<exclamation-circle-filled />
								该姓名用于转账验证，请按真实姓名填写，填写错误将转账失败
							</p>
						</m-form-item>
						<m-form-item label="充值账户手机号" name="phone">
							<m-input
								v-model:value="formState.phone"
								:maxlength="11"
								placeholder="请输入您的11位手机号"
							/>
						</m-form-item>
						<m-form-item label="充值账户与绑定驾校的关系" name="relationship">
							<m-select
								v-model:value="formState.relationship"
								:options="CONNECT_OPTIONS"
								placeholder="请选择您与驾校的关系"
							></m-select>
						</m-form-item>
						<div class="footer">
							<m-button type="primary" html-type="submit" :loading="authLoading">确定</m-button>
						</div>
					</m-form>
				</div>
			</div>
		</m-spin>
	</pm-dialog>
</template>

<script lang="ts" src="./index.ts"></script>
<style lang="less" src="./index.less" scoped></style>
