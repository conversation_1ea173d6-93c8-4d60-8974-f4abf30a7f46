export interface StateModel {
	authLoading: boolean;
	buyTimeout?: boolean;
	lastData?: LastDataModel;
	formState: formStateModel;
}

interface formStateModel {
	name: string;
	phone: string;
	relationship: string;
}

export interface PayAccountAuthParamsModel extends formStateModel {
	payNumber: string;
}

export interface LastDataModel {
	status: number;
	amount: number;
	typeShow: string;
	account: string;
}
