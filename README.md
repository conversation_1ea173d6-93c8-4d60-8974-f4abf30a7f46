## 项目的背景

商城中心，处理线上商品的购买，充值和交易

## 项目的术语定义

无

## 项目的部署

1. 驾校 PAAS 管理 - 应用列表 - buick

## 项目的运行环境

1. node >= 14
2. npm run serve
3. chrome 浏览器

## 项目的编码规范

项目内的 eslint、prettier

## 项目的技术选型

vue3，webpack, pinia, mitt, typeScript, qrcode

## 项目的逻辑视图

![buick流程图](https://web-resource.mc-cdn.cn/web/qiye-jkbd-v3/business/readme/buick.png!1000x0)

## 项目的注意事项

[通用注意事项](https://alidocs.dingtalk.com/i/nodes/XPwkYGxZV3zkv6mGfAZ01mXaWAgozOKL?utm_scene=person_space)

## 项目的参考资料

[PaaS buick](https://alidocs.dingtalk.com/i/nodes/6LeBq413JAQEmg4Li4AZOnqrJDOnGvpb)

[PaaS 开发文档](https://a.mucang.cn/?project=paas-docs#base.dashboard)

[paas 项目开发快速调试](https://alidocs.dingtalk.com/i/nodes/o14dA3GK8g6LY7AXi071p0yEJ9ekBD76?doc_type=wiki_doc&iframeQuery=utm_source%3Dportal%26utm_medium%3Dportal_recent&rnd=0.465812267369351&utm_medium=dingdoc_doc_plugin_url&utm_source=dingdoc_doc)
