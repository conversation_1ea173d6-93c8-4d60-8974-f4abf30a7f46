const prod = require('./app.env.config.json');
const dev = require('./app.env.dev.config.json');
const local = require('./app.env.local.config.json');
const test = require('./app.env.test.config.json');

module.exports = function (isProd, isDev, isLocal, isTest) {
	let config = {};
	if (isProd) {
		config = prod;
	} else if (isLocal) {
		config = local;
	} else if (isDev) {
		config = dev;
	} else if (isTest) {
		config = test;
	}
	return config;
};
